"""
Utility to suppress Tensor<PERSON>low and other warnings.
"""

import os
import logging
import warnings
import tensorflow as tf

def suppress_tensorflow_warnings():
    """
    Suppress TensorFlow and Keras warnings.
    """
    # Suppress TensorFlow logging
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
    
    # Disable oneDNN custom operations warning
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    
    # Suppress deprecation warnings
    tf.compat.v1.logging.set_verbosity(tf.compat.v1.logging.ERROR)
    
    # Suppress Keras warnings
    logging.getLogger('tensorflow').setLevel(logging.ERROR)
    logging.getLogger('keras').setLevel(logging.ERROR)
    logging.getLogger('absl').setLevel(logging.ERROR)
    
    # Suppress Python warnings
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=UserWarning)
    warnings.filterwarnings('ignore', category=DeprecationWarning)

def use_keras_native_format():
    """
    Configure TensorFlow to use the native Keras format for saving models.
    """
    # Set environment variable to use native Keras format
    os.environ['TF_KERAS_SAVE_FORMAT'] = 'keras'
