#!/usr/bin/env python3
"""
Test Trading Schedule Fix
Verify that the P&L calculation and trading schedule fixes work correctly.
"""

import sys
import os
import logging
from datetime import datetime, timezone

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

def test_pnl_calculation():
    """Test the fixed P&L calculation."""
    print("🧪 TESTING P&L CALCULATION FIX")
    print("=" * 50)
    
    try:
        from order_execution_system import OrderExecutionSystem
        
        # Create order executor
        print("📊 Creating order execution system...")
        order_executor = OrderExecutionSystem()
        
        # Test P&L calculation
        print("💰 Testing daily P&L calculation...")
        daily_pnl = order_executor._get_daily_closed_trades_pnl()
        
        print(f"✅ Daily P&L calculated: ${daily_pnl:.2f}")
        print(f"   This should now match MT5 history from 00:00 GMT today")
        
        return True
        
    except Exception as e:
        print(f"❌ P&L calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trading_allowance():
    """Test the trading allowance check."""
    print("\n🧪 TESTING TRADING ALLOWANCE CHECK")
    print("=" * 50)
    
    try:
        from order_execution_system import OrderExecutionSystem
        
        # Create order executor
        print("📊 Creating order execution system...")
        order_executor = OrderExecutionSystem()
        
        # Test trading allowance
        print("🎯 Testing trading allowance...")
        is_allowed = order_executor.is_trading_allowed()
        
        print(f"✅ Trading allowed: {is_allowed}")
        
        # Get current P&L for context
        order_executor._update_daily_pnl()
        current_pnl = order_executor.daily_pnl
        
        print(f"   Current daily P&L: ${current_pnl:.2f}")
        
        # Check limits
        import config
        max_profit = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_profit"]
        max_loss = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]
        
        print(f"   Daily profit limit: ${max_profit:.2f}")
        print(f"   Daily loss limit: -${max_loss:.2f}")
        
        if current_pnl >= max_profit:
            print(f"   🛑 PROFIT LIMIT REACHED - Trading should be STOPPED")
        elif current_pnl <= -max_loss:
            print(f"   🛑 LOSS LIMIT REACHED - Trading should be STOPPED")
        else:
            print(f"   ✅ Within limits - Trading should be ALLOWED")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading allowance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_pnl():
    """Test dashboard P&L display."""
    print("\n🧪 TESTING DASHBOARD P&L DISPLAY")
    print("=" * 50)
    
    try:
        from dashboard_server import DashboardDataManager
        
        # Create dashboard manager
        print("📊 Creating dashboard manager...")
        dashboard_manager = DashboardDataManager()
        
        # Initialize if needed
        if hasattr(dashboard_manager, 'initialize_trading_system'):
            dashboard_manager.initialize_trading_system()
        
        # Update trade metrics
        print("💰 Updating trade metrics...")
        dashboard_manager._update_trade_metrics()
        
        # Get dashboard data
        dashboard_data = dashboard_manager.get_dashboard_data()
        trade_metrics = dashboard_data.get("trade_metrics", {})
        
        daily_pnl = trade_metrics.get("daily_pnl", 0.0)
        print(f"✅ Dashboard daily P&L: ${daily_pnl:.2f}")
        print(f"   This should now match the fixed calculation")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard P&L test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 TRADING SCHEDULE & P&L FIX VERIFICATION")
    print("=" * 60)
    print(f"🕐 Test time: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S GMT')}")
    
    # Run tests
    tests = [
        ("P&L Calculation", test_pnl_calculation),
        ("Trading Allowance", test_trading_allowance),
        ("Dashboard P&L", test_dashboard_pnl),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 ALL FIXES WORKING CORRECTLY!")
        print("\n📊 Expected improvements:")
        print("   • Dashboard P&L now matches MT5 history")
        print("   • Trading stops when daily limits are reached")
        print("   • No more trading at 22:00 GMT")
        print("   • System only trades from 00:00 GMT until limits hit")
    else:
        print("⚠️  Some fixes need attention")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
