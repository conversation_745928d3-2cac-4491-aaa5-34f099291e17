{"timestamp": "2025-07-02T00:01:42.605534", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6878013014793396, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:01:42.668171", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:01:42.673196", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:01:42.884868", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6548080444335938, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:01:42.944648", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8479817596147582, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:01:42.954185", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:01:43.066055", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:01:43.125816", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.66, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:01:43.131308", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:04:52.820353", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8257724046707153, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:04:52.902612", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:04:52.907645", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:04:53.118751", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7900236248970032, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:04:53.179792", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9327060185251324, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:04:53.189634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999810457229614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:04:53.302696", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:04:53.361976", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.66, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:04:53.367186", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:08:02.870395", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9408345818519592, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:08:02.933029", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.985889751552795, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:08:02.938134", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:08:03.144527", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8137751221656799, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:08:03.206070", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8330392874630186, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:08:03.215623", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999843835830688, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:08:03.323051", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:08:03.382531", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.54, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:08:03.387567", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:11:12.766608", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975588321685791, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:11:12.828023", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:11:12.833053", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:11:13.039328", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8178176879882812, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:11:13.100261", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7860474220460837, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:11:13.132446", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999847412109375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:11:13.239791", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:11:13.327302", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.54, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:11:13.332323", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:14:22.633457", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975958466529846, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:14:22.703551", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:14:22.708333", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:14:22.913951", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8472623229026794, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:14:22.977567", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8316492534835287, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:14:22.985815", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999817609786987, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:14:23.104493", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:14:23.164857", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.54, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:14:23.170126", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:17:32.688735", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8111658096313477, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:17:32.749005", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.994563492063492, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:17:32.765567", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:17:32.967499", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7732580304145813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:17:33.026465", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8596785310249525, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:17:33.036097", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999748468399048, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:17:33.168676", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:17:33.228083", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:17:33.259336", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:20:42.558609", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9845637083053589, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:20:42.618213", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7283890031101532, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:20:42.640333", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:20:42.838769", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8273926973342896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:20:42.897963", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7987490671043251, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:20:42.908097", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999758005142212, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:20:43.017665", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:20:43.077504", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:20:43.118686", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:23:40.758850", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.69158536195755, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:23:41.002877", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9950714285714285, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:23:41.007904", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:23:41.062998", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7941731810569763, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:23:41.123415", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7221344072955681, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:23:41.132450", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999887943267822, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:23:41.204517", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:23:41.270153", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:23:41.275609", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:26:40.753262", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.956572949886322, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:26:40.823725", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9935885225885225, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:26:40.876679", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:26:40.948885", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8233054280281067, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:26:41.009333", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7240596107792966, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:26:41.018447", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:26:41.090399", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:26:41.155596", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:26:41.160632", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:29:40.740943", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.946774423122406, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:29:40.815077", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:29:40.820145", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:29:40.876866", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8378610610961914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:29:40.937812", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8854198356124692, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:29:40.946936", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999819993972778, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:29:41.018330", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:29:41.083042", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:29:41.088075", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:32:40.830529", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9927229285240173, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:32:40.892388", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9974242424242425, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:32:40.897143", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:32:40.954662", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7593616843223572, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:32:41.014469", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9194133291189711, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:32:41.024004", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99997878074646, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:32:41.095070", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999481439590454, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:32:41.161350", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:32:41.166428", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:35:40.851173", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5139288306236267, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:35:40.927620", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:35:41.124345", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:35:41.195141", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8107797503471375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:35:41.255317", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8300408842918996, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:35:41.264347", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999659061431885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:35:41.337348", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999979734420776, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:35:41.403331", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:35:41.408424", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:38:41.120417", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.45754149556159973, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:38:41.396926", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.984342132505176, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:38:41.401282", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:38:41.457502", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8256486058235168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:38:41.518012", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7656394107173626, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:38:41.528243", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999715089797974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:38:41.599553", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:38:41.665132", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:38:41.670154", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:41:40.763156", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8111700415611267, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:41:40.834202", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666665, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:41:40.841323", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:41:40.895363", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8600295782089233, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:41:40.953251", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8360026889322387, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:41:40.988827", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999767541885376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:41:41.093872", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:41:41.153524", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:41:41.187273", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:44:40.818154", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7748923301696777, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:44:41.012599", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666665, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:44:41.017604", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:44:41.073659", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8442730903625488, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:44:41.133373", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8242976124022304, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:44:41.142382", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999983549118042, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:44:41.214768", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:44:41.279695", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:44:41.285208", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:47:40.728617", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5635386109352112, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:47:40.794661", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9717312409812408, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:47:40.949336", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:47:41.020512", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.252032995223999, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:47:41.078759", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5487132293770245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:47:41.120665", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999573230743408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:47:41.223442", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999805688858032, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:47:41.281933", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:47:41.307095", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:50:40.757500", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7422593832015991, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:50:41.163502", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.540231144288567, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:50:41.168277", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:50:41.222384", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6616528630256653, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:50:41.280888", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5018198963939818, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:50:41.311081", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:50:41.381741", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:50:41.446015", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:50:41.472195", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:53:40.719046", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5090007781982422, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:53:40.936707", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:53:40.942580", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:53:40.995214", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7354404926300049, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:53:41.053848", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7052821346077224, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:53:41.076143", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:53:41.177207", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999946355819702, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:53:41.235562", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:53:41.262557", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:56:40.807147", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.48776960372924805, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:56:40.879280", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:56:40.884312", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:56:40.939751", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7296754121780396, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:56:41.000115", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7486766857909735, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:56:41.009411", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999380111694336, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:56:41.081230", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:56:41.144817", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:56:41.149838", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T00:59:40.862168", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9054352045059204, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T00:59:40.923844", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T00:59:40.929280", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T00:59:40.985203", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6907033920288086, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T00:59:41.046444", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8406274669294967, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T00:59:41.055734", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99998939037323, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T00:59:41.127654", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T00:59:41.195013", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T00:59:41.200030", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:02:41.225344", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6093754172325134, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:02:41.308032", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9833531746031745, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:02:41.313092", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:02:41.369036", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.23618347942829132, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:02:41.429314", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.44493208525426925, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:02:41.439408", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999483823776245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:02:41.512980", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9996285438537598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:02:41.577074", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:02:41.582109", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:05:40.736476", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9383798241615295, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:05:40.817942", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9589797008547007, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:05:40.822005", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:05:40.879819", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7493365406990051, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:05:40.939120", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.47241714333520535, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:05:40.949130", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999548196792603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:05:41.020379", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999918937683105, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:05:41.084162", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:05:41.089167", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:08:41.017310", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4904479384422302, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:08:41.117944", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9985714285714287, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:08:41.123370", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:08:41.179849", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6710602641105652, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:08:41.239672", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6280938445991917, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:08:41.248757", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999967098236084, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:08:41.320123", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999957084655762, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:08:41.385034", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:08:41.390057", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:11:40.708732", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5197006464004517, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:11:40.865348", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:11:40.870991", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:11:40.924297", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9339615106582642, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:11:40.984019", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6336765681623734, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:11:41.007764", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:11:41.110752", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999951124191284, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:11:41.170789", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:11:41.193955", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:14:40.835377", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5020506381988525, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:14:41.158034", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994444444444445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:14:41.163451", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:14:41.217873", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8020979166030884, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:14:41.278578", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6469088748009324, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:14:41.287841", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999767541885376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:14:41.359411", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:14:41.424706", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:14:41.430763", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:17:40.714394", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7188351154327393, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:17:40.977445", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9645201465201464, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:17:40.981476", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:17:41.035650", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6946350932121277, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:17:41.094144", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49679510622448453, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:17:41.125324", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999586343765259, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:17:41.196130", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999961853027344, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:17:41.261253", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:17:41.286341", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:20:40.727628", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9190938472747803, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:20:40.789865", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7299303660948998, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:20:40.795588", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:20:40.848683", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7282366752624512, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:20:40.907467", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.604459434159827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:20:40.938918", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999630451202393, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:20:41.009318", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:20:41.074452", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:20:41.099441", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:23:40.819436", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6837444305419922, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:23:40.891562", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9886507936507937, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:23:40.896589", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:23:40.952638", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8506008982658386, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:23:41.014784", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5252827750335244, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:23:41.022873", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999638795852661, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:23:41.094946", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:23:41.158876", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:23:41.163956", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:26:40.911008", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6162585020065308, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:26:40.972447", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:26:40.977508", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:26:41.033151", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8488339185714722, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:26:41.092051", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5462217689819342, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:26:41.101091", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999527931213379, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:26:41.172469", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:26:41.235994", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:26:41.241028", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:29:40.810941", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5954412817955017, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:29:40.881098", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:29:40.886103", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:29:40.946662", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8488814234733582, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:29:41.009472", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6656850793583938, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:29:41.019242", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999657869338989, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:29:41.091920", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:29:41.156030", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:29:41.162340", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:32:41.058747", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9420833587646484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:32:41.205445", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9520873015873015, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:32:41.209465", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:32:41.267345", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8945809602737427, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:32:41.327290", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.2904429744265654, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:32:41.336707", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999600648880005, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:32:41.407938", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999618530273438, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:32:41.474673", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:32:41.479892", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:35:40.821527", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6533181667327881, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:35:40.898538", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9762563381338133, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:35:40.903683", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:35:40.959353", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8916809558868408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:35:41.019157", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7385382644346845, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:35:41.028299", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999589920043945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:35:41.102282", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999980926513672, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:35:41.165957", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:35:41.172130", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:38:40.722181", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8315151333808899, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:38:40.826334", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.98875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:38:40.831393", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:38:40.886273", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8854712843894958, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:38:40.944778", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8415656738359535, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:38:40.967159", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999748468399048, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:38:41.038059", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:38:41.104974", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:38:41.123366", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:41:40.765844", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.740452766418457, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:41:40.841582", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984801587301588, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:41:40.846698", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:41:40.901105", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8891311287879944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:41:40.960819", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8393501327451861, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:41:40.978048", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999794960021973, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:41:41.048376", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:41:41.113497", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:41:41.134842", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:44:40.805421", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9486148953437805, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:44:40.868404", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9987301587301587, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:44:41.317921", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:44:41.388574", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8987923860549927, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:44:41.449963", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7721051779504029, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:44:41.459069", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:44:41.531912", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:44:41.594620", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:44:41.599659", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:47:40.756005", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8779051303863525, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:47:41.046589", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9943722099217767, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:47:41.050484", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:47:41.104019", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8562358617782593, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:47:41.162930", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.395897859139716, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:47:41.193911", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999735355377197, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:47:41.264143", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999977350234985, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:47:41.326748", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:47:41.353974", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:50:40.717812", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.9381350874900818, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:50:40.966554", "model_name": "short_term_momentum_rf", "signal": -2, "confidence": 0.4190634840847654, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:50:40.971616", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:50:41.027020", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8529695868492126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:50:41.087409", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.26613871274796613, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:50:41.096512", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999963641166687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:50:41.168744", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:50:41.233718", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:50:41.238778", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:53:40.714451", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.8396468758583069, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:53:40.776449", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:53:40.781960", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:53:40.837608", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8724333643913269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:53:40.898117", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4515867853972896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:53:40.906755", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999268054962158, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:53:40.978244", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:53:41.041219", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:53:41.046224", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:56:40.770360", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.9250546097755432, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:56:40.834202", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:56:40.839271", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:56:40.895304", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9377627968788147, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:56:40.954984", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.46843038465852216, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:56:40.964068", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998842477798462, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:56:41.035637", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:56:41.100273", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:56:41.105512", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T01:59:40.793777", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.9372502565383911, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T01:59:40.863075", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9969397590361446, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T01:59:40.868559", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T01:59:40.925074", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9391594529151917, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T01:59:40.984795", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.46958792148061673, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T01:59:40.993864", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999593496322632, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T01:59:41.066506", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T01:59:41.129212", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T01:59:41.134258", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:02:40.808567", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9939025640487671, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:02:40.907742", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:02:40.912778", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:02:40.968691", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.808834433555603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:02:41.028004", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9610144834649144, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:02:41.037047", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999558925628662, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:02:41.108383", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999202489852905, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:02:41.173888", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:02:41.178926", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:05:40.782862", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8231083750724792, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:05:41.030017", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:05:41.035045", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:05:41.090658", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8518129587173462, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:05:41.152038", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9413833441214274, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:05:41.161573", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999933123588562, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:05:41.232324", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:05:41.295652", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:05:41.300687", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:08:40.727821", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.59644615650177, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:08:40.799003", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9884095928226363, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:08:40.803096", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:08:40.857059", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8564831018447876, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:08:40.916870", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8566003210133346, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:08:40.945773", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999810457229614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:08:41.016638", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:08:41.082893", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:08:41.100971", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:11:40.780063", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9907649755477905, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:11:41.057739", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9926875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:11:41.063230", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:11:41.116631", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.876476526260376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:11:41.176324", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.938168745477683, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:11:41.197338", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999817609786987, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:11:41.269705", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:11:41.335748", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:11:41.340783", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:14:40.737974", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9793302416801453, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:14:40.808331", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9945, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:14:40.813392", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:14:40.869512", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8820686936378479, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:14:40.929761", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.920681548476927, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:14:40.938806", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:14:41.009892", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:14:41.074560", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:14:41.079612", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:17:40.824081", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8850969076156616, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:17:40.918604", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9536642262965793, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:17:40.923882", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:17:40.977377", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.940790593624115, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:17:41.037510", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7136309092745827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:17:41.057601", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999737739562988, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:17:41.161834", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999964237213135, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:17:41.222218", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:17:41.227244", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:20:40.988469", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.801740288734436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:20:41.067030", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.954657599285669, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:20:41.072066", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:20:41.128086", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9427146911621094, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:20:41.188919", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7172236571003476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:20:41.198271", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999963641166687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:20:41.270343", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999972581863403, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:20:41.334688", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:20:41.340059", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:23:40.925697", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9314653873443604, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:23:40.987025", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9379229239075787, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:23:40.992096", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:23:41.048501", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.90544193983078, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:23:41.108783", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5487524620688545, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:23:41.118376", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999221563339233, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:23:41.190541", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:23:41.254153", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:23:41.259416", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:26:40.891151", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9675922989845276, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:26:41.089082", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9776666666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:26:41.094108", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:26:41.150140", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9021093249320984, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:26:41.209794", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5877132234664508, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:26:41.219004", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999082088470459, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:26:41.293511", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:26:41.357382", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:26:41.362817", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:29:40.870655", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.969884991645813, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:29:41.114165", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9819848484848486, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:29:41.121326", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:29:41.177273", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9419134855270386, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:29:41.237145", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5250164160519639, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:29:41.246723", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999544620513916, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:29:41.317787", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:29:41.385061", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:29:41.390100", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:32:40.803235", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7195507287979126, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:32:41.153218", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9830411255411255, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:32:41.158401", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:32:41.214074", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6738836169242859, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:32:41.274594", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5601190015107775, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:32:41.283006", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999593496322632, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:32:41.354575", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999953508377075, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:32:41.422329", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:32:41.427836", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:35:40.760022", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9274957180023193, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:35:40.821547", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6822273049422563, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:35:40.915221", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:35:40.985021", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6857020258903503, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:35:41.043340", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5034403949860918, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:35:41.086325", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:35:41.190864", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:35:41.251113", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:35:41.273365", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:38:40.821698", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8319789171218872, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:38:41.133872", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9963888888888889, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:38:41.138795", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:38:41.191817", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7122774720191956, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:38:41.254017", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.474126475568863, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:38:41.280830", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999699592590332, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:38:41.350811", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:38:41.415708", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:38:41.441329", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:41:40.796995", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.5401363372802734, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:41:41.133854", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:41:41.138421", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:41:41.191790", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7008985877037048, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:41:41.250248", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.47510986717725456, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:41:41.277826", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999619722366333, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:41:41.350396", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:41:41.414034", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:41:41.419071", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:44:40.916930", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.853731095790863, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:44:40.979378", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:44:40.984422", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:44:41.040193", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7699053287506104, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:44:41.102535", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4994947269796841, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:44:41.110571", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:44:41.183141", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:44:41.246040", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:44:41.252178", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:47:40.758658", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9807369709014893, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:47:40.822999", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9955555555555555, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:47:40.828067", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:47:40.884470", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.2968279719352722, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:47:40.945411", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6551441348935542, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:47:40.954531", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999438524246216, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:47:41.027102", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998843669891357, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:47:41.090429", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:47:41.095474", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:50:40.879964", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9137129783630371, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:50:40.943557", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9857386363636362, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:50:40.948577", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:50:41.004060", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7636131644248962, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:50:41.064896", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6369146628049234, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:50:41.073001", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999505281448364, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:50:41.145573", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999876022338867, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:50:41.208887", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:50:41.213902", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:53:40.838065", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6027172207832336, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:53:41.045744", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:53:41.229726", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:53:41.287262", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.741523802280426, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:53:41.347024", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7485266388554292, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:53:41.356232", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999649524688721, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:53:41.429299", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:53:41.492965", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:53:41.497565", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:56:40.812660", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5529277920722961, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:56:40.893553", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:56:40.898396", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:56:40.952185", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7294487953186035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:56:41.010318", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.828137802209808, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:56:41.045502", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999831914901733, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:56:41.148368", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999951124191284, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:56:41.209816", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:56:41.243834", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T02:59:40.766063", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.48698699474334717, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T02:59:41.115624", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T02:59:41.120630", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T02:59:41.175680", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.773909866809845, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T02:59:41.235674", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8351713933749781, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T02:59:41.244695", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999847412109375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T02:59:41.316990", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T02:59:41.379974", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T02:59:41.385237", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:02:40.818995", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5628541111946106, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:02:40.882272", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9839583333333334, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:02:40.902607", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:02:40.960535", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9225171208381653, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:02:41.021462", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6292983769792322, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:02:41.030531", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999569654464722, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:02:41.102207", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999909400939941, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:02:41.167712", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:02:41.172272", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:05:40.732219", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5579231381416321, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:05:40.803592", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.608012394894721, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:05:40.807487", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:05:40.862953", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8575040698051453, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:05:40.944289", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.615396293351558, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:05:40.953157", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999691247940063, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:05:41.024652", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999996423721313, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:05:41.113066", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:05:41.118875", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:08:40.777968", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8129697442054749, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:08:40.844500", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.955625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:08:40.849565", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:08:40.905607", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8871817588806152, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:08:40.966306", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4313235249313728, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:08:40.975399", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999709129333496, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:08:41.047256", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:08:41.113129", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:08:41.118161", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:11:40.830477", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8274392485618591, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:11:40.898592", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.988989898989899, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:11:40.903654", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:11:40.961079", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8853797316551208, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:11:41.020540", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3938786469676988, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:11:41.029634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999982476234436, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:11:41.101314", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:11:41.165480", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:11:41.170531", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:14:40.863078", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9241583943367004, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:14:40.948255", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9837301587301588, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:14:40.953627", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:14:41.010066", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8991983532905579, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:14:41.070204", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.44654270223510617, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:14:41.079742", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:14:41.152817", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:14:41.219003", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:14:41.224388", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:17:40.952836", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9026901125907898, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:17:41.017514", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.965630305152364, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:17:41.022553", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:17:41.079009", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7822496891021729, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:17:41.139452", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.4948861496310089, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:17:41.147517", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999386072158813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:17:41.219180", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999179840087891, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:17:41.282849", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:17:41.288300", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:20:40.807632", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9251667261123657, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:20:41.263877", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9479442640692644, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:20:41.268948", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:20:41.324855", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8424338698387146, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:20:41.385975", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.4988623714496181, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:20:41.395323", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999173879623413, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:20:41.466388", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999979734420776, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:20:41.533288", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:20:41.538348", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:23:40.727502", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9206944108009338, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:23:40.923531", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9849572118702554, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:23:40.929007", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:23:40.984930", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8521033525466919, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:23:41.044806", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7774059014682166, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:23:41.054813", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999605417251587, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:23:41.127580", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:23:41.193159", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:23:41.198893", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:26:40.760791", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.997222900390625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:26:40.909646", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:26:40.914636", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:26:40.969120", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.869899570941925, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:26:41.027931", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9221847242045186, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:26:41.055151", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999808073043823, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:26:41.125352", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:26:41.189105", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:26:41.215253", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:29:40.735118", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9650896787643433, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:29:40.799958", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:29:40.804039", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:29:40.860944", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8748295307159424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:29:40.920837", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9043310720891431, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:29:40.929879", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999794960021973, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:29:41.001551", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:29:41.065380", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:29:41.070628", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:32:40.832008", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.49158725142478943, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:32:40.896215", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9850886384856973, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:32:40.901621", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:32:40.957536", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.611935019493103, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:32:41.017740", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8697640142222772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:32:41.026810", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999620914459229, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:32:41.098379", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9996752738952637, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:32:41.161964", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:32:41.167121", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:35:40.786583", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8304519653320312, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:35:41.062559", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.951010461760462, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:35:41.067929", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:35:41.122606", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8069489002227783, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:35:41.183406", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7632197968657068, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:35:41.192541", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999462366104126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:35:41.266116", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999908208847046, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:35:41.331304", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:35:41.336725", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:38:40.875143", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8820690512657166, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:38:40.937774", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9896349206349205, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:38:40.943005", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:38:40.997510", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8245195746421814, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:38:41.057821", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6158566738333079, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:38:41.066889", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999815225601196, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:38:41.137457", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999994158744812, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:38:41.200744", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:38:41.206200", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:41:40.876620", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6512216925621033, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:41:40.938723", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9942777777777778, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:41:40.943860", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:41:40.999224", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.831409215927124, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:41:41.060294", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6481009865092624, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:41:41.070345", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999810457229614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:41:41.141962", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999946355819702, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:41:41.205839", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:41:41.211119", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:44:40.741656", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.610261857509613, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:44:40.844670", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:44:41.216760", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:44:41.273986", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8369251489639282, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:44:41.333964", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.701091779194098, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:44:41.343041", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999850988388062, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:44:41.414107", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:44:41.478631", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:44:41.483655", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:47:40.732402", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5976161360740662, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:47:41.298075", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9411219336219335, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:47:41.303105", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:47:41.357987", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7309306263923645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:47:41.417066", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5412311267535688, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:47:41.446108", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999685287475586, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:47:41.516676", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999949932098389, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:47:41.580223", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:47:41.602243", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:50:40.754331", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5819122791290283, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:50:40.894252", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.990125, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:50:40.899306", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:50:40.954397", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7200390696525574, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:50:41.014807", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5272697742867508, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:50:41.023886", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999411106109619, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:50:41.095457", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999935626983643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:50:41.161648", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:50:41.166671", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:53:40.796496", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7335048913955688, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:53:40.989580", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9870556526806528, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:53:40.993666", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:53:41.047992", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8857777714729309, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:53:41.106839", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6003696021447638, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:53:41.137535", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.5289437174797058, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:53:41.207599", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999983310699463, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:53:41.272391", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:53:41.297662", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:56:40.768685", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9302148818969727, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:56:40.849399", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9939685314685315, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:56:40.854498", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:56:40.910906", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8814369440078735, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:56:40.971395", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5643751275298405, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:56:40.980613", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8396928310394287, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:56:41.053184", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999983310699463, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:56:41.117248", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:56:41.122273", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T03:59:40.917036", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9599571824073792, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T03:59:40.988611", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.995, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T03:59:41.009998", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T03:59:41.069943", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8835513591766357, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T03:59:41.130959", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4935079408849431, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T03:59:41.140542", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8865801095962524, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T03:59:41.213671", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999984502792358, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T03:59:41.277369", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.68, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T03:59:41.282873", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:02:40.943519", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9969794750213623, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:02:41.004774", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9934608303343243, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:02:41.010024", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:02:41.067202", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.765801191329956, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:02:41.127002", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9534343305531866, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:02:41.136255", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999976396560669, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:02:41.209911", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999956488609314, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:02:41.276011", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.67, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:02:41.281049", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:05:40.821258", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6654579639434814, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:05:40.931664", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9907242063492063, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:05:41.547252", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:05:41.617477", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8118848204612732, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:05:41.676833", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9331524710029073, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:05:41.686942", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999815225601196, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:05:41.758006", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999996542930603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:05:41.821565", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.67, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:05:41.826593", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:08:40.767094", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7194266319274902, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:08:41.279303", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9916635610766045, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:08:41.284395", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:08:41.340026", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8218084573745728, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:08:41.401339", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9159533408806614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:08:41.410379", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999886751174927, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:08:41.481744", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:08:41.545289", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.67, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:08:41.550343", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:11:41.075532", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9960801005363464, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:11:41.267853", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:11:41.272567", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:11:41.326437", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8476778268814087, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:11:41.385009", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9038553398066281, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:11:41.415321", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999914169311523, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:11:41.487088", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:11:41.550515", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.675, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:11:41.575069", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:14:40.800780", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9922534823417664, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:14:41.076967", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:14:41.082399", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:14:41.138326", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8328679800033569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:14:41.198938", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8798292813185423, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:14:41.207441", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999902248382568, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:14:41.280011", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:14:41.345785", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.68, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:14:41.350979", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:17:40.775701", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7618343234062195, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:17:40.836848", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9881440781440783, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:17:40.842021", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:17:40.899549", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7234479784965515, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:17:40.959273", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.5234513871828755, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:17:40.967385", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999806880950928, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:17:41.040011", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999890327453613, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:17:41.105231", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.71, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:17:41.110235", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:20:40.909407", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7501606941223145, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:20:40.970437", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9854354395604398, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:20:40.975442", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:20:41.031997", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7546197175979614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:20:41.091712", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6658686977432078, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:20:41.101226", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999775886535645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:20:41.172796", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999909400939941, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:20:41.238541", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.71, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:20:41.243599", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:23:40.969960", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5496746301651001, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:23:41.032540", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:23:41.038578", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:23:41.095059", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.761424720287323, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:23:41.156799", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.831631211804138, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:23:41.166030", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999831914901733, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:23:41.239391", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999994158744812, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:23:41.302163", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.71, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:23:41.307231", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:26:40.833966", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9732528924942017, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:26:40.920773", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:26:40.926166", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:26:40.983159", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.734168529510498, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:26:41.043293", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7985976678758465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:26:41.052336", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999747276306152, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:26:41.126905", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999946355819702, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:26:41.191662", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.71, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:26:41.196974", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:29:40.960188", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.942425549030304, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:29:41.024860", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:29:41.030140", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:29:41.086757", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.731012225151062, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:29:41.147688", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8972195145746571, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:29:41.156758", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999771118164062, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:29:41.229335", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:29:41.294795", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.71, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:29:41.300280", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:32:40.792792", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4899705946445465, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:32:41.013694", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9684322081427346, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:32:41.640405", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:32:41.711646", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7910096049308777, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:32:41.770575", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6861982680756933, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:32:41.779584", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999649524688721, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:32:41.851650", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:32:41.917404", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.7, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:32:41.922409", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:35:40.780766", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9945118427276611, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:35:40.858447", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8433961003185917, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:35:40.862696", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:35:40.916543", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.826761782169342, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:35:40.976996", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7607187886912254, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:35:41.011919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999768733978271, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:35:41.082951", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999996423721313, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:35:41.146874", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.7, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:35:41.172817", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:38:40.782664", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9961121678352356, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:38:40.973196", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994444444444445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:38:40.978254", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:38:41.032452", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8548988699913025, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:38:41.093387", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.44150766224215365, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:38:41.122555", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9443556070327759, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:38:41.193635", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999912977218628, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:38:41.257399", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.7, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:38:41.283482", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:41:40.845520", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9963394403457642, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:41:41.452460", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9969444444444444, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:41:41.456402", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:41:41.511946", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8701438903808594, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:41:41.574769", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4977456781862791, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:41:41.588524", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999094009399414, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:41:41.658199", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999922513961792, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:41:41.721669", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.7, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:41:41.744706", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:44:40.808852", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5013319849967957, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:44:40.904392", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:44:41.519600", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:44:41.589518", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8965732455253601, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:44:41.649404", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6591443396827801, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:44:41.692883", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998780488967896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:44:41.796263", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:44:41.854530", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:44:41.879777", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:47:40.853053", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7076473832130432, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:47:40.942538", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9820748556998558, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:47:40.948605", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:47:41.003521", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8413717746734619, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:47:41.062736", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6103646890056862, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:47:41.071793", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:47:41.143431", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999548196792603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:47:41.208912", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:47:41.213979", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:50:40.892704", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8185214996337891, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:50:40.967432", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.966015873015873, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:50:40.972653", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:50:41.028628", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9168761372566223, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:50:41.088386", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.607193442227684, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:50:41.097706", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:50:41.169273", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999954700469971, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:50:41.231260", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:50:41.236825", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:53:40.940380", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7206875681877136, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:53:41.002565", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9391332695082696, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:53:41.134382", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:53:41.191999", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9431649446487427, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:53:41.255298", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5386214697645868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:53:41.263836", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999821186065674, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:53:41.335901", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:53:41.400377", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:53:41.405407", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:56:40.837545", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.528225302696228, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:56:40.905903", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9947321428571427, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:56:40.911073", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:56:40.967414", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9428672790527344, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:56:41.027336", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.43470776988109505, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:56:41.036793", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:56:41.108302", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:56:41.171730", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:56:41.177876", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T04:59:40.791902", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8761093616485596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T04:59:40.853743", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9947321428571427, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T04:59:40.858749", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T04:59:40.916305", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9421944618225098, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T04:59:40.975002", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4329654158677154, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T04:59:40.984464", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999981164932251, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T04:59:41.056132", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T04:59:41.121173", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.685, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T04:59:41.126715", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:02:40.869106", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5885360836982727, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:02:41.081860", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9945194805194805, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:02:41.087186", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:02:41.142382", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6703707575798035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:02:41.204265", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7833739192503661, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:02:41.212756", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999637603759766, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:02:41.284822", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999953508377075, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:02:41.352443", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:02:41.357640", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:05:40.837486", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9235237240791321, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:05:40.928831", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7706452515401488, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:05:41.540960", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:05:41.612465", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6934243440628052, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:05:41.671408", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7294611554854029, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:05:41.681004", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999715089797974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:05:41.752669", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:05:41.818519", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:05:41.823571", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:08:40.795189", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9453774094581604, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:08:40.867753", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9983333333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:08:40.872905", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:08:40.926332", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7343534827232361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:08:40.985496", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9071434502218965, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:08:41.007786", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999833106994629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:08:41.111646", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:08:41.172972", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:08:41.193526", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:11:40.844543", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9739900231361389, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:11:40.949839", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:11:41.146112", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:11:41.216984", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6998767256736755, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:11:41.277875", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9040513244827121, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:11:41.286918", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999821186065674, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:11:41.358985", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999954700469971, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:11:41.425074", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:11:41.430134", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:14:40.824723", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7630650401115417, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:14:40.922075", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:14:40.927183", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:14:40.983286", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6869157552719116, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:14:41.043646", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.907674483105407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:14:41.053199", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999868869781494, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:14:41.124025", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:14:41.189058", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:14:41.194572", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:17:40.859672", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7736155390739441, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:17:40.923486", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9377380952380951, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:17:40.979274", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:17:41.038970", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8744746446609497, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:17:41.098630", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4567562366922948, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:17:41.108178", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999967098236084, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:17:41.180307", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999809265136719, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:17:41.244374", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:17:41.248387", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:20:40.784256", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6116130948066711, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:20:41.413639", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9599127685180318, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:20:41.417755", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:20:41.472934", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8383944630622864, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:20:41.532300", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5862026976214225, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:20:41.541626", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999931812286377, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:20:41.613809", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999939203262329, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:20:41.676309", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:20:41.681587", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:23:40.771953", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8565298318862915, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:23:40.903333", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9834253246753248, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:23:40.908181", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:23:40.963609", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8587790727615356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:23:41.022080", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.43233882615961056, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:23:41.051190", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999802112579346, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:23:41.122760", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:23:41.189235", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:23:41.194239", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:26:40.769266", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7031400799751282, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:26:40.911970", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9956230158730157, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:26:40.917216", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:26:40.972624", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8791065812110901, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:26:41.034065", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.512946878383643, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:26:41.043199", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999903440475464, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:26:41.113808", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:26:41.178679", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:26:41.183873", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:29:40.855370", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8039963841438293, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:29:41.121468", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9987301587301587, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:29:41.126534", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:29:41.179981", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8987843990325928, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:29:41.240010", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.46168003356184445, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:29:41.268579", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999840259552002, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:29:41.340224", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:29:41.404526", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:29:41.429136", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:32:40.871391", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8213346600532532, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:32:40.962848", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9946713519354708, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:32:40.969015", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:32:41.024392", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.89579176902771, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:32:41.085701", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3577824281123172, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:32:41.095151", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999600648880005, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:32:41.166215", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999985694885254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:32:41.231019", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:32:41.236317", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:35:40.892729", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9361160397529602, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:35:40.957951", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9909156549971767, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:35:40.962971", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:35:41.020221", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8872877955436707, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:35:41.082801", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3329943568069367, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:35:41.091347", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999587535858154, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:35:41.161916", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999986886978149, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:35:41.227612", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:35:41.232630", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:38:40.963545", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9984357953071594, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:38:41.027225", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9950662251655629, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:38:41.032754", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:38:41.089216", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8943871259689331, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:38:41.149060", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3958048684306933, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:38:41.158100", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.916606068611145, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:38:41.229166", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999946355819702, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:38:41.295257", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:38:41.300277", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:41:40.918124", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9984682202339172, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:41:40.983107", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:41:40.988192", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:41:41.043133", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9036869406700134, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:41:41.103503", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.38681248725721135, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:41:41.113087", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8858779668807983, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:41:41.185152", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:41:41.247924", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:41:41.252981", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:44:40.920854", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9985301494598389, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:44:40.993006", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.996, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:44:40.998135", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:44:41.057166", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9016900062561035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:44:41.119819", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.41444240010850064, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:44:41.129360", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8105871081352234, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:44:41.205429", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:44:41.272313", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:44:41.277383", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:47:40.774220", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8962711691856384, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:47:40.836263", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9946428571428573, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:47:40.842167", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:47:40.895824", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.670691192150116, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:47:40.972736", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7522106719670965, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:47:40.981907", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999357461929321, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:47:41.052567", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:47:41.127327", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:47:41.132408", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:50:40.770287", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8155419826507568, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:50:41.170160", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.502630097260685, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:50:41.174181", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:50:41.227676", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7258827686309814, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:50:41.287672", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6876165604491905, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:50:41.707749", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999746084213257, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:50:41.779569", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:50:41.844135", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:50:41.875648", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:53:40.781546", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7733500599861145, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:53:40.861369", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9913888888888889, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:53:40.866401", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:53:40.921813", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8475561141967773, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:53:40.982168", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6544427869735493, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:53:40.990301", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999788999557495, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:53:41.061870", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:53:41.130287", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:53:41.135340", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:56:40.868764", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9428900480270386, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:56:40.930651", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9925, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:56:40.940458", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:56:40.998436", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8474829196929932, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:56:41.057492", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6508754812883594, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:56:41.066533", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:56:41.139263", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:56:41.203964", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:56:41.209510", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T05:59:40.920138", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9624080061912537, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T05:59:40.988564", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9925, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T05:59:40.993757", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T05:59:41.049709", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8486326932907104, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T05:59:41.110524", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5726529980740621, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T05:59:41.119601", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999779462814331, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T05:59:41.191732", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T05:59:41.256865", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T05:59:41.262192", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:02:40.925155", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8642404079437256, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:02:41.001791", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9468730158730158, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:02:41.006816", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:02:41.063801", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8665705323219299, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:02:41.123855", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.44329818115341996, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:02:41.132928", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999617338180542, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:02:41.205082", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999840497970581, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:02:41.270526", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:02:41.275551", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:05:40.845928", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5613272190093994, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:05:40.928845", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9501062783002439, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:05:40.934057", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:05:40.991611", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.88507080078125, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:05:41.052774", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.48110689886962255, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:05:41.062038", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999971866607666, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:05:41.133922", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999918937683105, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:05:41.200404", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:05:41.205463", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:08:40.953788", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6189046502113342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:08:41.018087", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8898214285714288, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:08:41.024093", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:08:41.080240", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8466724753379822, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:08:41.140432", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6629529203138388, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:08:41.149442", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999715089797974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:08:41.220516", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:08:41.283727", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:08:41.288744", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:11:40.804819", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8352794647216797, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:11:41.425692", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9840555555555557, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:11:41.430942", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:11:41.486730", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8722372055053711, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:11:41.546940", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5120533381232969, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:11:41.556729", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999797344207764, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:11:41.626670", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:11:41.690332", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:11:41.695371", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:14:40.958671", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9847103953361511, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:14:41.106095", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.986984126984127, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:14:41.110313", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:14:41.164302", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8775704503059387, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:14:41.224342", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6751489296950562, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:14:41.247225", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999842643737793, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:14:41.319445", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:14:41.384105", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:14:41.403145", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:17:40.866423", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8038139343261719, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:17:41.288799", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9736107053872941, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:17:41.292887", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:17:41.349435", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8623122572898865, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:17:41.408715", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4494302494965555, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:17:41.418178", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999688863754272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:17:41.491906", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:17:41.556050", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:17:41.561177", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:20:40.797069", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8001709580421448, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:20:40.892411", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9845165945165946, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:20:40.897124", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:20:40.952457", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8629578351974487, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:20:41.011633", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.484168176828945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:20:41.041415", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999839067459106, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:20:41.112984", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:20:41.172996", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:20:41.178001", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:23:40.915746", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9889477491378784, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:23:40.980156", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:23:41.305152", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:23:41.376718", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8937897086143494, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:23:41.435928", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5233644085605537, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:23:41.444982", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8495257496833801, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:23:41.516550", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999971389770508, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:23:41.582263", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:23:41.588287", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:26:40.916067", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9903704524040222, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:26:40.993580", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:26:40.998669", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:26:41.055113", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9012656807899475, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:26:41.114994", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5225778011385578, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:26:41.124096", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9579827785491943, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:26:41.195665", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999971389770508, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:26:41.259669", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:26:41.264767", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:29:53.081928", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9908087253570557, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:29:53.143326", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:29:53.148330", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:29:53.358584", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8988199234008789, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:29:53.418924", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5221000405064449, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:29:53.428934", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9581995606422424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:29:53.537543", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999977350234985, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:29:53.597984", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:29:53.602990", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:33:02.808136", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4855785369873047, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:33:02.868107", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9803333333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:33:02.873137", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:33:03.080809", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7538034319877625, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:33:03.140743", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8411732763000244, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:33:03.149790", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999791383743286, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:33:03.257593", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999704360961914, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:33:03.318350", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:33:03.323886", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:36:13.083285", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4736119508743286, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:36:13.144010", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.85205697548066, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:36:13.149016", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:36:13.361280", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7087664604187012, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:36:13.422442", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8699317019974301, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:36:13.432189", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999573230743408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:36:13.540793", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999998807907104, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:36:13.600159", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:36:13.605164", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:39:22.947419", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5872767567634583, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:39:23.007727", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9988888888888888, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:39:23.012761", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:39:23.219697", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7392033934593201, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:39:23.281412", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9210638484457527, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:39:23.290974", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999843835830688, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:39:23.399198", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:39:23.458830", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:39:23.463911", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:42:32.929071", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9196954965591431, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:42:32.989808", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:42:32.994813", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:42:33.205014", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7718700170516968, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:42:33.265358", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.921753903443523, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:42:33.274368", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999823570251465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:42:33.382584", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:42:33.442363", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:42:33.447368", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:45:42.850889", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9166532158851624, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:45:42.911391", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:45:42.916396", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:45:43.120966", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7929220795631409, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:45:43.219738", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9178931396862074, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:45:43.227930", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999756813049316, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:45:43.336333", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:45:43.412501", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:45:43.417338", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:48:52.907828", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9245588183403015, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:48:52.966400", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9766884920634921, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:48:52.971978", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:48:53.209946", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7229804396629333, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:48:53.271421", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7962260393918903, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:48:53.281557", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999669790267944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:48:53.391182", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999902248382568, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:48:53.451221", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:48:53.456247", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:52:02.770793", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8489707112312317, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:52:02.829915", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9822222222222223, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:52:02.848247", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:52:03.050361", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8354758024215698, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:52:03.108345", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6613057526532757, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:52:03.117502", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999709129333496, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:52:03.223489", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:52:03.282920", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:52:03.287956", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:55:12.867699", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9199305772781372, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:55:12.928819", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:55:12.933995", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:55:13.140337", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8445324301719666, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:55:13.200143", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6613396011577126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:55:13.209459", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:55:13.318855", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:55:13.409915", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:55:13.414725", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T06:58:22.746426", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8941608667373657, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T06:58:22.807045", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T06:58:22.812222", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T06:58:23.017932", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8448072075843811, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T06:58:23.078819", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.750147073437317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T06:58:23.109054", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999982476234436, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T06:58:23.233913", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T06:58:23.294310", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T06:58:23.326462", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:01:32.853261", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8973397016525269, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:01:32.913447", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9793313492063492, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:01:32.918579", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:01:33.120140", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6639272570610046, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:01:33.210656", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7764690545473715, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:01:33.220574", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999582767486572, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:01:33.329380", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999083280563354, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:01:33.402546", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.665, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:01:33.407692", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:04:42.799998", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4948643445968628, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:04:42.860357", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9737217643467644, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:04:42.868614", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:04:43.104634", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7399833798408508, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:04:43.163395", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7741970302087761, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:04:43.173242", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999604225158691, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:04:43.282266", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999996542930603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:04:43.339801", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.68, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:04:43.344675", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:07:52.814823", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8323216438293457, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:07:52.884948", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9886507936507937, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:07:52.890119", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:07:53.093298", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8355051875114441, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:07:53.152900", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6267799976182278, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:07:53.182522", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999721050262451, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:07:53.297279", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:07:53.355777", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.685, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:07:53.366733", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:11:02.832212", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9252151250839233, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:11:02.895900", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:11:02.900789", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:11:03.102345", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8487316966056824, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:11:03.162281", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6279667414299717, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:11:03.171725", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999692440032959, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:11:03.285745", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:11:03.346097", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:11:03.350625", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:14:12.900209", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5113357901573181, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:14:13.021848", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994444444444445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:14:13.027046", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:14:13.237014", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8087744116783142, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:14:13.296616", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8001563563676816, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:14:13.306724", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999829530715942, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:14:13.416764", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:14:13.476943", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:14:13.482181", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:17:22.795449", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6229865550994873, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:17:22.872754", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:17:22.876970", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:17:23.083623", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8526739478111267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:17:23.146077", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6628209888075116, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:17:23.154736", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999637603759766, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:17:23.266933", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:17:23.327271", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:17:23.332625", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:20:32.931585", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.47210338711738586, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:20:32.992755", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9913198051948052, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:20:32.998071", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:20:33.207618", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8647918701171875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:20:33.268410", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6594563761452348, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:20:33.278787", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998899698257446, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:20:33.385007", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:20:33.445129", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:20:33.450352", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:23:42.986026", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5852704048156738, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:23:43.047715", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9989204545454545, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:23:43.053222", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:23:43.267503", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8704224228858948, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:23:43.327488", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.45926191944588834, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:23:43.337420", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.5368335247039795, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:23:43.444650", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:23:43.504412", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:23:43.509975", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:26:52.950184", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6037423014640808, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:26:53.012178", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:26:53.017332", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:26:53.225620", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8680274486541748, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:26:53.288609", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6320725597688012, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:26:53.297673", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8847859501838684, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:26:53.407393", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:26:53.466592", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:26:53.472305", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:30:02.929548", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5766341090202332, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:30:02.990720", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.996, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:30:02.996128", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:30:03.203612", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8657015562057495, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:30:03.264791", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7212974374596091, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:30:03.275111", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9265931248664856, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:30:03.383328", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999990463256836, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:30:03.442857", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.69, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:30:03.447878", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:33:13.095780", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.735040545463562, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:33:13.156243", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998901098901099, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:33:13.161267", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:33:13.372092", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.722030758857727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:33:13.432557", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6234913647932839, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:33:13.441587", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999414682388306, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:33:13.551681", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999657869338989, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:33:13.611675", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:33:13.617211", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:36:23.594052", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8204941153526306, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:36:23.654076", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7401327875555813, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:36:23.659081", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:36:23.866281", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.766791045665741, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:36:23.925936", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.67743713252161, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:36:23.935451", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:36:24.042654", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999998807907104, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:36:24.101980", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:36:24.106985", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:39:47.361479", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8315718770027161, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:39:47.422787", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9616031746031746, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:39:47.447956", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:39:47.658463", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7685734033584595, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:39:47.719092", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7621048515561415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:39:47.729127", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:39:47.837024", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:39:47.897526", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:39:47.901544", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:43:43.270195", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8624343872070312, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:43:43.331160", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9889285714285714, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:43:43.337620", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:43:43.546671", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.763391375541687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:43:43.660746", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7897151495756434, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:43:43.670421", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999876022338867, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:43:43.782179", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:43:43.842697", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:43:43.847445", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:46:53.346527", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5287075042724609, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:46:53.407004", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9768472222222222, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:46:53.412076", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:46:53.619311", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.43763577938079834, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:46:53.680912", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7333098593944183, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:46:53.691121", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999579191207886, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:46:53.798743", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9991040825843811, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:46:53.857574", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:46:53.862099", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:50:03.137856", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8884785771369934, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:50:03.198195", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.94594681013431, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:50:03.203686", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:50:03.416557", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.776746392250061, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:50:03.477278", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7917472113236598, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:50:03.486393", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999505281448364, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:50:03.601358", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:50:03.662629", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:50:03.667663", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:53:13.247103", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8302569389343262, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:53:13.307643", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9735045093795094, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:53:13.312693", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:53:13.521295", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7656097412109375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:53:13.582262", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8990658673283644, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:53:13.591315", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:53:13.698445", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999957084655762, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:53:13.758146", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:53:13.763169", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:56:23.438167", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6859614253044128, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:56:23.498924", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.987, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:56:23.503946", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:56:23.713358", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7379302382469177, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:56:23.773615", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8977823496477878, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:56:23.782661", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999976634979248, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:56:23.892584", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999960660934448, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:56:23.951699", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:56:23.957216", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T07:59:33.145170", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.692859411239624, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T07:59:33.206011", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9936666666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T07:59:33.211255", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T07:59:33.420986", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7448737025260925, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T07:59:33.481863", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9358335740144978, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T07:59:33.490936", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99998939037323, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T07:59:33.602020", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T07:59:33.661112", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T07:59:33.666454", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:02:43.038313", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7794545292854309, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:02:43.099676", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9581498015873016, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:02:43.104990", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:02:43.313295", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9091922640800476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:02:43.374852", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5619311145783893, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:02:43.383906", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999574422836304, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:02:43.492089", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998736381530762, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:02:43.551798", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:02:43.556821", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:05:53.049520", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.48208075761795044, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:05:53.110652", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9747704240204241, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:05:53.115689", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:05:53.323792", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9046722650527954, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:05:53.385298", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5758944578412402, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:05:53.394843", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:05:53.503489", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999940395355225, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:05:53.563768", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:05:53.568823", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:09:03.382145", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5581318140029907, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:09:03.443145", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9809033613445379, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:09:03.448516", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:09:03.657171", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8766710162162781, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:09:03.717899", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7462453635594738, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:09:03.727156", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999833106994629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:09:03.837874", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:09:03.898896", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:09:03.904231", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:12:13.148181", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.557773768901825, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:12:13.284557", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9943028083028084, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:12:13.290696", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:12:13.499704", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8897234201431274, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:12:13.560609", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5479325045218222, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:12:13.570196", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:12:13.680917", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999998807907104, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:12:13.740233", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:12:13.745476", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:15:23.398188", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9751792550086975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:15:23.458142", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.995017094017094, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:15:23.463213", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:15:23.672015", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8990710377693176, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:15:23.733011", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.44574060699143303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:15:23.742054", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999897480010986, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:15:23.852297", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999998807907104, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:15:23.912459", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:15:23.917946", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:18:33.201404", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9007978439331055, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:18:33.265028", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.971839826839827, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:18:33.270058", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:18:33.479034", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8580565452575684, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:18:33.542434", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8311382674323592, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:18:33.552501", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999184608459473, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:18:33.662207", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999953508377075, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:18:33.721949", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:18:33.726987", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:21:44.036915", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5352160930633545, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:21:44.100590", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5943004784182326, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:21:44.105614", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:21:44.318759", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7383570671081543, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:21:44.382176", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8751457457967678, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:21:44.391343", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999768733978271, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:21:44.507776", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999996423721313, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:21:44.569616", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:21:44.575795", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T08:24:53.877805", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8085024356842041, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T08:24:53.940542", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9822321428571428, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T08:24:53.945689", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T08:24:54.158665", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8408039212226868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T08:24:54.220824", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8426629864055777, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T08:24:54.230943", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T08:24:54.345087", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T08:24:54.406697", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T08:24:54.412204", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T11:43:07.969981", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.2901269495487213, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T11:43:08.270688", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.4249627147166891, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T11:43:08.275382", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T11:43:08.480947", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.793601930141449, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T11:43:08.543083", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6018334579332961, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T11:43:08.551637", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999186992645264, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T11:43:08.661641", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999901056289673, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T11:43:08.723121", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T11:43:08.728938", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T11:55:59.955634", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4882890284061432, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T11:56:00.018690", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9125269754739513, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T11:56:00.023716", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T11:56:00.259206", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6864210963249207, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T11:56:00.323523", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.41246552596779557, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T11:56:00.332786", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999941349029541, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T11:56:00.446809", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999953508377075, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T11:56:00.509205", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T11:56:00.514287", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T12:33:56.865551", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9783869981765747, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T12:33:56.926663", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.3814891064777678, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T12:33:56.932097", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T12:33:57.141683", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7209599018096924, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T12:33:57.204866", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.4982879958622842, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T12:33:57.214088", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999653100967407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T12:33:57.321028", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T12:33:57.382126", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.51, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T12:33:57.386647", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T12:36:55.084623", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6692606806755066, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T12:36:55.389282", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.941669191233603, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T12:36:55.394805", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T12:36:55.450824", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.619873046875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T12:36:55.510426", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.3990086953655645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T12:36:55.520719", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999981164932251, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T12:36:55.596510", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999935626983643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T12:36:55.660786", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T12:36:55.665895", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-02T12:39:55.158254", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6692606806755066, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-02T12:39:55.223140", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.941669191233603, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-02T12:39:55.228188", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-02T12:39:55.286339", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.619873046875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-02T12:39:55.347236", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.3990086953655645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-02T12:39:55.356793", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999981164932251, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-02T12:39:55.436438", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999935626983643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-02T12:39:55.509232", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-02T12:39:55.514287", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
