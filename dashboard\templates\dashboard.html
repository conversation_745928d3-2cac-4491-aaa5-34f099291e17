<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading System Dashboard - DEX 900 DOWN Index</title>
    
    <!-- Chart.js for price charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Bootstrap for styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background-color: #0d1117;
            color: #c9d1d9;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #1e2328 0%, #2d3748 100%);
            border-bottom: 2px solid #30363d;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .card {
            background-color: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #21262d;
            border-bottom: 1px solid #30363d;
            font-weight: 600;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-operational { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .status-unknown { background-color: #6c757d; }
        
        .metric-card {
            text-align: center;
            padding: 1rem;
            background-color: #0d1117;
            border: 1px solid #21262d;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            background-color: #161b22;
            border-color: #30363d;
        }

        .risk-section-divider {
            border-bottom: 2px solid #495057;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
        }

        .risk-section-header {
            color: #f0f6fc;
            font-weight: 600;
            margin-bottom: 1rem;
            padding: 0.5rem 0;
            border-left: 4px solid #238636;
            padding-left: 1rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #8b949e;
        }
        
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #ffc107; }
        
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .model-card {
            background-color: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 1rem;
        }
        
        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .model-name {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .model-status {
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .status-active { background-color: #28a745; color: white; }
        .status-inactive { background-color: #6c757d; color: white; }
        .status-error { background-color: #dc3545; color: white; }
        
        .signal-indicator {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .signal-buy { background-color: rgba(40, 167, 69, 0.2); color: #28a745; }
        .signal-sell { background-color: rgba(220, 53, 69, 0.2); color: #dc3545; }
        .signal-hold { background-color: rgba(108, 117, 125, 0.2); color: #6c757d; }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 1rem;
        }
        
        .timeframe-tabs {
            margin-bottom: 1rem;
        }
        
        .last-update {
            font-size: 0.8rem;
            color: #8b949e;
            text-align: right;
        }
        
        .ensemble-prediction {
            background: linear-gradient(135deg, #1e2328 0%, #2d3748 100%);
            border: 2px solid #30363d;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .ensemble-consensus {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .ensemble-strength {
            font-size: 1.2rem;
            color: #8b949e;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #8b949e;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 1rem;
        }

        .source-indicator {
            background: #007bff;
            color: white;
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 5px;
        }

        .source-indicator[title*="LIVE_TRADING_CACHE"] {
            background: #28a745; /* Green for live trading data */
        }

        .source-indicator[title*="DASHBOARD_GENERATED"] {
            background: #ffc107; /* Yellow for dashboard generated */
            color: #000;
        }

        .source-indicator[title*="CACHE_MISS"] {
            background: #dc3545; /* Red for cache miss */
        }

        @media (max-width: 768px) {
            .model-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-value {
                font-size: 1.5rem;
            }
        }

        .position-item {
            background-color: #161b22;
            border-color: #30363d !important;
            transition: all 0.2s ease;
        }

        .position-item:hover {
            background-color: #21262d;
            border-color: #58a6ff !important;
        }

        .position-item .fw-bold {
            font-size: 0.9rem;
        }

        .position-item .small {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        AI Trading System Dashboard
                    </h1>
                    <p class="mb-0 text-muted">DEX 900 DOWN Index - 9 Model Ensemble</p>
                </div>
                <div class="col-md-6">
                    <div class="text-end">
                        <div id="system-status" class="mb-2">
                            <span class="status-indicator status-unknown"></span>
                            <span id="status-text">Initializing...</span>
                        </div>
                        <div class="last-update">
                            Last Update: <span id="last-update">--:--:--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Current Price and Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card">
                    <div class="card-body metric-card">
                        <div id="current-price" class="metric-value">---.--</div>
                        <div class="metric-label">Current Price</div>
                        <div id="price-change" class="mt-2">
                            <span id="change-value">---.--</span>
                            <span id="change-percent">(--.--%)</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card">
                    <div class="card-body metric-card">
                        <div id="account-balance" class="metric-value">$0.00</div>
                        <div class="metric-label">Account Balance</div>
                        <div id="daily-pnl" class="mt-2">
                            Daily: <span>$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card">
                    <div class="card-body metric-card">
                        <div id="win-rate" class="metric-value">--%</div>
                        <div class="metric-label">Win Rate</div>
                        <div id="trade-count" class="mt-2">
                            Trades: <span>0</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-line me-2"></i>Active Positions
                            <span id="active-positions-count" class="badge bg-primary ms-2">0</span>
                        </h6>
                        <div id="active-positions-details" class="mt-3">
                            <div class="text-muted text-center">No active positions</div>
                        </div>
                        <div id="account-balance" class="mt-3 pt-2 border-top">
                            <small class="text-muted">Balance: <span>$10,000</span></small>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Timeframe Consensus -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="ensemble-prediction">
                    <h4><i class="fas fa-clock me-2"></i>Short Term</h4>
                    <div id="short-term-consensus" class="ensemble-consensus" style="font-size: 1.8rem;">ANALYZING...</div>
                    <div id="short-term-strength" class="ensemble-strength">Consensus: ---%</div>
                    <div class="mt-2">
                        <small id="short-term-models">0/3 models</small>
                    </div>
                    <div id="short-term-strong" class="mt-1" style="font-size: 0.8rem; color: #ffd700;"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="ensemble-prediction">
                    <h4><i class="fas fa-clock me-2"></i>Medium Term</h4>
                    <div id="medium-term-consensus" class="ensemble-consensus" style="font-size: 1.8rem;">ANALYZING...</div>
                    <div id="medium-term-strength" class="ensemble-strength">Consensus: ---%</div>
                    <div class="mt-2">
                        <small id="medium-term-models">0/3 models</small>
                    </div>
                    <div id="medium-term-strong" class="mt-1" style="font-size: 0.8rem; color: #ffd700;"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="ensemble-prediction">
                    <h4><i class="fas fa-clock me-2"></i>Long Term</h4>
                    <div id="long-term-consensus" class="ensemble-consensus" style="font-size: 1.8rem;">ANALYZING...</div>
                    <div id="long-term-strength" class="ensemble-strength">Consensus: ---%</div>
                    <div class="mt-2">
                        <small id="long-term-models">0/3 models</small>
                    </div>
                    <div id="long-term-strong" class="mt-1" style="font-size: 0.8rem; color: #ffd700;"></div>
                </div>
            </div>
        </div>

        <!-- Price Charts -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Live Price Charts</h5>
                    </div>
                    <div class="card-body">
                        <!-- Timeframe Tabs -->
                        <ul class="nav nav-tabs timeframe-tabs" id="timeframeTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="1M-tab" data-bs-toggle="tab" data-bs-target="#chart-1M" type="button" role="tab">1M</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="5M-tab" data-bs-toggle="tab" data-bs-target="#chart-5M" type="button" role="tab">5M</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="15M-tab" data-bs-toggle="tab" data-bs-target="#chart-15M" type="button" role="tab">15M</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="1H-tab" data-bs-toggle="tab" data-bs-target="#chart-1H" type="button" role="tab">1H</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="1D-tab" data-bs-toggle="tab" data-bs-target="#chart-1D" type="button" role="tab">1D</button>
                            </li>
                        </ul>
                        
                        <!-- Chart Content -->
                        <div class="tab-content" id="timeframeTabContent">
                            <div class="tab-pane fade show active" id="chart-1M" role="tabpanel">
                                <div class="chart-container">
                                    <canvas id="priceChart1M"></canvas>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="chart-5M" role="tabpanel">
                                <div class="chart-container">
                                    <canvas id="priceChart5M"></canvas>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="chart-15M" role="tabpanel">
                                <div class="chart-container">
                                    <canvas id="priceChart15M"></canvas>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="chart-1H" role="tabpanel">
                                <div class="chart-container">
                                    <canvas id="priceChart1H"></canvas>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="chart-1D" role="tabpanel">
                                <div class="chart-container">
                                    <canvas id="priceChart1D"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Trading Status -->
        <div class="row mb-4" id="live-trading-section" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Live Trading Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div id="trades-executed" class="metric-value">0</div>
                                    <div class="metric-label">Trades Executed</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div id="signals-generated" class="metric-value">0</div>
                                    <div class="metric-label">Signals Generated</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div id="last-signal" class="metric-value">--</div>
                                    <div class="metric-label">Last Signal</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div id="risk-status" class="metric-value">SAFE</div>
                                    <div class="metric-label">Risk Status</div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div id="last-trade-info" class="alert alert-info" style="display: none;">
                                    <strong>Last Trade:</strong> <span id="last-trade-details">No trades yet</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Models Analysis -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-brain me-2"></i>AI Models Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div id="models-grid" class="model-grid">
                            <!-- Models will be populated by JavaScript -->
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> Loading AI models...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>System Health</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-health">
                            <div class="loading">Loading system health...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Management -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risk Management</h5>
                    </div>
                    <div class="card-body">
                        <div id="risk-metrics">
                            <div class="loading">Loading risk metrics...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Dashboard Functionality -->
    <script src="{{ url_for('static', filename='dashboard.js') }}"></script>
</body>
</html>
