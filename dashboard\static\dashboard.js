/**
 * AI Trading Dashboard JavaScript
 * Handles real-time data updates and chart rendering
 */

class TradingDashboard {
    constructor() {
        this.charts = {};
        this.updateInterval = 180000; // 3 minutes in milliseconds
        this.lastUpdate = null;
        this.isUpdating = false;
        
        // Initialize dashboard
        this.init();
    }
    
    init() {
        console.log('🚀 Initializing AI Trading Dashboard...');
        
        // Initial data load
        this.updateDashboard();
        
        // Set up auto-refresh
        setInterval(() => {
            this.updateDashboard();
        }, this.updateInterval);
        
        // Initialize charts
        this.initializeCharts();
        
        console.log('✅ Dashboard initialized successfully');
    }
    
    async updateDashboard() {
        if (this.isUpdating) {
            console.log('⏳ Update already in progress, skipping...');
            return;
        }
        
        this.isUpdating = true;
        
        try {
            console.log('🔄 Updating dashboard data...');
            
            const response = await fetch('/api/data');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Update all dashboard sections
            this.updateSystemStatus(data);
            this.updateCurrentPrice(data);
            this.updateTradeMetrics(data);
            this.updateTimeframeConsensus(data);
            this.updatePriceCharts(data);
            this.updateModelsAnalysis(data);
            this.updateSystemHealth(data);
            this.updatePerformanceMetrics(data);
            this.updateRiskMetrics(data);
            this.updateLiveTradingStatus(data);
            
            this.lastUpdate = new Date();
            this.updateLastUpdateTime();
            
            console.log('✅ Dashboard updated successfully');
            
        } catch (error) {
            console.error('❌ Error updating dashboard:', error);
            this.showError('Failed to update dashboard data');
        } finally {
            this.isUpdating = false;
        }
    }
    
    updateSystemStatus(data) {
        const statusElement = document.getElementById('status-text');
        const indicatorElement = document.querySelector('.status-indicator');
        
        if (statusElement && indicatorElement) {
            const status = data.system_status || 'UNKNOWN';
            statusElement.textContent = status;
            
            // Update status indicator
            indicatorElement.className = 'status-indicator';
            if (status === 'OPERATIONAL') {
                indicatorElement.classList.add('status-operational');
            } else if (status.includes('ERROR')) {
                indicatorElement.classList.add('status-error');
            } else if (status === 'INITIALIZING') {
                indicatorElement.classList.add('status-warning');
            } else {
                indicatorElement.classList.add('status-unknown');
            }
        }
    }
    
    updateCurrentPrice(data) {
        const currentPrice = data.current_price;
        if (!currentPrice) return;
        
        // Update price
        const priceElement = document.getElementById('current-price');
        if (priceElement) {
            priceElement.textContent = currentPrice.price.toFixed(2);
        }
        
        // Update change
        const changeValueElement = document.getElementById('change-value');
        const changePercentElement = document.getElementById('change-percent');
        
        if (changeValueElement && changePercentElement) {
            const change = currentPrice.change;
            const changePct = currentPrice.change_pct;
            
            changeValueElement.textContent = (change >= 0 ? '+' : '') + change.toFixed(2);
            changePercentElement.textContent = `(${(changePct >= 0 ? '+' : '')}${changePct.toFixed(2)}%)`;
            
            // Update colors
            const changeContainer = document.getElementById('price-change');
            if (changeContainer) {
                changeContainer.className = change >= 0 ? 'mt-2 positive' : 'mt-2 negative';
            }
        }
    }
    
    updateTradeMetrics(data) {
        const metrics = data.trade_metrics;
        if (!metrics) return;
        
        // Update P&L
        const totalPnlElement = document.getElementById('total-pnl');
        if (totalPnlElement) {
            const pnl = metrics.total_pnl || 0;
            totalPnlElement.textContent = `$${pnl.toFixed(2)}`;
            totalPnlElement.className = `metric-value ${pnl >= 0 ? 'positive' : 'negative'}`;
        }
        
        // Update daily P&L
        const dailyPnlElement = document.querySelector('#daily-pnl span');
        if (dailyPnlElement) {
            const dailyPnl = metrics.daily_pnl || 0;
            dailyPnlElement.textContent = `$${dailyPnl.toFixed(2)}`;
            dailyPnlElement.className = dailyPnl >= 0 ? 'positive' : 'negative';
        }
        
        // Update win rate
        const winRateElement = document.getElementById('win-rate');
        if (winRateElement) {
            const winRate = metrics.win_rate || 0;
            winRateElement.textContent = `${(winRate * 100).toFixed(1)}%`;
        }
        
        // Update trade count
        const tradeCountElement = document.querySelector('#trade-count span');
        if (tradeCountElement) {
            tradeCountElement.textContent = metrics.total_trades || 0;
        }
        
        // Update active positions count
        const activePositionsCountElement = document.getElementById('active-positions-count');
        if (activePositionsCountElement) {
            activePositionsCountElement.textContent = metrics.active_positions || 0;
        }

        // Update detailed active positions
        this.updateActivePositionsDetails(metrics.position_details);
        
        // Update account balance
        const balanceElement = document.querySelector('#account-balance span');
        if (balanceElement) {
            const balance = metrics.account_balance || 10000;
            balanceElement.textContent = `$${balance.toLocaleString()}`;
        }
    }
    


    updateTimeframeConsensus(data) {
        const timeframeConsensus = data.timeframe_consensus;
        if (!timeframeConsensus) {
            console.log("No timeframe_consensus data received");
            return;
        }

        // Debug: Log received consensus data
        console.log("=== TIMEFRAME CONSENSUS DATA ===");
        console.log(timeframeConsensus);
        console.log("=== END CONSENSUS DATA ===");

        // Update each timeframe group
        ['short_term', 'medium_term', 'long_term'].forEach(timeframe => {
            const consensus = timeframeConsensus[timeframe];
            if (!consensus) return;

            // Update consensus signal
            const consensusElement = document.getElementById(`${timeframe.replace('_', '-')}-consensus`);
            if (consensusElement) {
                const signal = consensus.consensus_signal || 'ANALYZING';
                consensusElement.textContent = signal;

                // Update color based on signal
                consensusElement.className = 'ensemble-consensus';
                if (signal.includes('BUY')) {
                    consensusElement.classList.add('positive');
                } else if (signal.includes('SELL')) {
                    consensusElement.classList.add('negative');
                } else {
                    consensusElement.classList.add('neutral');
                }
            }

            // Update consensus strength
            const strengthElement = document.getElementById(`${timeframe.replace('_', '-')}-strength`);
            if (strengthElement) {
                const strength = consensus.consensus_strength || 0;
                strengthElement.textContent = `Consensus: ${strength.toFixed(1)}%`;
            }

            // Update contributing models and trade counts (REMOVED DAILY LIMITS)
            const modelsElement = document.getElementById(`${timeframe.replace('_', '-')}-models`);
            if (modelsElement) {
                const contributing = consensus.contributing_models || 0;
                const dailyTrades = consensus.daily_trades || 0;
                const monthlyTrades = consensus.monthly_trades || 0;
                // REMOVED: Daily limits - No trade limits per timeframe

                modelsElement.innerHTML = `${contributing}/3 models<br><small>Daily: ${dailyTrades} | Monthly: ${monthlyTrades}</small>`;
            }

            // Update strong signals indicator
            const strongElement = document.getElementById(`${timeframe.replace('_', '-')}-strong`);
            if (strongElement) {
                const strongSignals = consensus.strong_signals || [];
                if (strongSignals.length > 0) {
                    const strongText = strongSignals.map(s => {
                        const modelShort = s.model.split('_').pop().toUpperCase();
                        const signalText = s.signal > 0 ? 'BUY' : 'SELL';
                        const signalType = s.type === 'ULTRA_STRONG' ? '⚡' : '🤝';
                        const typeText = s.type === 'ULTRA_STRONG' ? 'ULTRA' : 'CONSENSUS';
                        return `${signalType} ${modelShort}: ${typeText} ${signalText} (${(s.confidence * 100).toFixed(1)}%)`;
                    }).join('<br>');
                    strongElement.innerHTML = strongText;
                } else {
                    strongElement.innerHTML = '';
                }
            }
        });
    }

    updateModelsAnalysis(data) {
        const models = data.model_predictions;
        if (!models) return;

        const modelsGrid = document.getElementById('models-grid');
        if (!modelsGrid) return;

        // Clear existing content
        modelsGrid.innerHTML = '';

        // Define the desired order: Short → Medium → Long
        const modelOrder = [
            // Short-term models
            'short_term_pattern_nn',
            'short_term_momentum_rf',
            'short_term_reversion_gb',
            // Medium-term models
            'medium_term_trend_lstm',
            'medium_term_breakout_rf',
            'medium_term_volatility_xgb',
            // Long-term models
            'long_term_macro_dnn',
            'long_term_levels_rf',
            'long_term_portfolio_gb'
        ];

        // Create model cards in the specified order
        modelOrder.forEach(modelName => {
            if (models[modelName]) {
                const modelCard = this.createModelCard(modelName, models[modelName]);
                modelsGrid.appendChild(modelCard);
            }
        });

        // Add any models not in the predefined order (fallback)
        Object.entries(models).forEach(([modelName, modelData]) => {
            if (!modelOrder.includes(modelName)) {
                const modelCard = this.createModelCard(modelName, modelData);
                modelsGrid.appendChild(modelCard);
            }
        });
    }
    
    createModelCard(modelName, modelData) {
        const card = document.createElement('div');
        card.className = 'model-card';
        
        // Determine status
        let statusClass = 'status-inactive';
        let statusText = 'INACTIVE';
        
        if (modelData.status === 'ACTIVE') {
            statusClass = 'status-active';
            statusText = 'ACTIVE';
        } else if (modelData.status && modelData.status.includes('ERROR')) {
            statusClass = 'status-error';
            statusText = 'ERROR';
        }
        
        // Determine signal
        let signalClass = 'signal-hold';
        let signalText = 'HOLD';
        
        if (modelData.signal) {
            if (modelData.signal > 0) {
                signalClass = 'signal-buy';
                signalText = `BUY ${modelData.signal}`;
            } else if (modelData.signal < 0) {
                signalClass = 'signal-sell';
                signalText = `SELL ${Math.abs(modelData.signal)}`;
            }
        }
        
        // Add source indicator for debugging
        const sourceIndicator = modelData.source ? `<span class="source-indicator" title="Data Source">${modelData.source}</span>` : '';

        card.innerHTML = `
            <div class="model-header">
                <div class="model-name">${modelName.replace(/_/g, ' ').toUpperCase()}</div>
                <div class="model-status ${statusClass}">${statusText}</div>
            </div>
            <div class="model-purpose">${modelData.purpose || 'Unknown purpose'}</div>
            <div class="signal-indicator ${signalClass}">${signalText}</div>
            <div class="model-details">
                <small>
                    Confidence: ${modelData.confidence ? (modelData.confidence * 100).toFixed(1) + '%' : 'N/A'}<br>
                    Type: ${modelData.model_type || 'Unknown'}<br>
                    Last: ${modelData.last_prediction || 'Never'}<br>
                    ${sourceIndicator}
                </small>
            </div>
        `;
        
        return card;
    }
    
    updateSystemHealth(data) {
        const health = data.system_health;
        if (!health) return;
        
        const healthContainer = document.getElementById('system-health');
        if (!healthContainer) return;
        
        healthContainer.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <div class="mb-2">
                        <span class="status-indicator ${this.getStatusClass(health.mt5_connection)}"></span>
                        MT5 Connection: ${health.mt5_connection || 'UNKNOWN'}
                    </div>
                    <div class="mb-2">
                        <span class="status-indicator ${this.getStatusClass(health.data_feed)}"></span>
                        Data Feed: ${health.data_feed || 'UNKNOWN'}
                    </div>
                </div>
                <div class="col-6">
                    <div class="mb-2">
                        <span class="status-indicator ${this.getStatusClass(health.models_status)}"></span>
                        Models: ${health.models_status || 'UNKNOWN'}
                    </div>
                    <div class="mb-2">
                        <small>Memory: ${health.memory_usage || 0}% | CPU: ${health.cpu_usage || 0}%</small>
                    </div>
                </div>
            </div>
        `;
    }
    
    updatePerformanceMetrics(data) {
        // Update performance metrics in the top row using trade_metrics data
        const tradeMetrics = data.trade_metrics || {};

        // Removed: Max Drawdown, Sharpe Ratio, Profit Factor, and Average Trade blocks
        // These metrics have been removed from the dashboard per user request
    }
    
    updateRiskMetrics(data) {
        const risk = data.risk_metrics;
        if (!risk) return;

        const riskContainer = document.getElementById('risk-metrics');
        if (!riskContainer) return;

        // Get risk level color class
        const getRiskLevelClass = (level) => {
            switch(level) {
                case 'LOW': return 'text-success';
                case 'MEDIUM': return 'text-warning';
                case 'HIGH': return 'text-danger';
                default: return 'text-secondary';
            }
        };

        // Get P&L color class
        const getPnLClass = (pnl) => {
            return pnl > 0 ? 'text-success' : pnl < 0 ? 'text-danger' : 'text-secondary';
        };

        riskContainer.innerHTML = `
            <!-- SL/TP Settings -->
            <div class="risk-section-divider">
                <div class="col-12">
                    <h6 class="risk-section-header"><i class="fas fa-crosshairs me-2"></i>Stop Loss / Take Profit Settings</h6>
                </div>
                <div class="col-12">
                    <div class="row">
                        ${this.generateSLTPSettings(risk.sl_tp_settings)}
                    </div>
                </div>
            </div>

            <!-- Circuit Breakers -->
            <div class="row">
                <div class="col-12">
                    <h6 class="risk-section-header"><i class="fas fa-shield-alt me-2"></i>Circuit Breakers & Limits</h6>
                </div>
                <div class="col-12">
                    <div class="row">
                        ${this.generateCircuitBreakers(risk.circuit_breakers)}
                    </div>
                </div>
            </div>
        `;
    }



    generateSLTPSettings(slTpSettings) {
        if (!slTpSettings) return '<div class="col-12 text-muted">No SL/TP data available</div>';

        // Define the desired order: Short → Medium → Long
        const timeframeOrder = ['short_term', 'medium_term', 'long_term'];

        return timeframeOrder.map(timeframe => {
            const settings = slTpSettings[timeframe];
            if (!settings) return '';

            return `
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="metric-label">${timeframe.replace('_', ' ').toUpperCase()}</div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="text-success">
                                    <strong>BUY</strong><br>
                                    <small>SL: ${settings.buy.sl_points}pts ($${settings.buy.sl_dollars.toFixed(2)})</small><br>
                                    <small>TP: ${settings.buy.tp_points}pts ($${settings.buy.tp_dollars.toFixed(2)})</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-danger">
                                    <strong>SELL</strong><br>
                                    <small>SL: ${settings.sell.sl_points}pts ($${settings.sell.sl_dollars.toFixed(2)})</small><br>
                                    <small>TP: ${settings.sell.tp_points}pts ($${settings.sell.tp_dollars.toFixed(2)})</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).filter(html => html !== '').join('');
    }

    generateCircuitBreakers(circuitBreakers) {
        if (!circuitBreakers) return '<div class="col-12 text-muted">No circuit breaker data available</div>';

        const breakers = [
            { label: 'Max Daily Loss', value: circuitBreakers.max_daily_loss },
            { label: 'Max Concurrent', value: circuitBreakers.max_concurrent },
            { label: 'Drawdown Limit', value: circuitBreakers.drawdown_limit },
            { label: 'One Per Timeframe', value: circuitBreakers.one_per_timeframe },
            { label: 'MT5 Connection', value: circuitBreakers.mt5_connection }
        ];

        return breakers.map(breaker => `
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value">${breaker.value || 'N/A'}</div>
                    <div class="metric-label">${breaker.label}</div>
                </div>
            </div>
        `).join('');
    }

    updateActivePositionsDetails(positionDetails) {
        const detailsContainer = document.getElementById('active-positions-details');
        if (!detailsContainer) return;

        if (!positionDetails || !positionDetails.positions || positionDetails.positions.length === 0) {
            detailsContainer.innerHTML = '<div class="text-muted text-center">No active positions</div>';
            return;
        }

        const positions = positionDetails.positions;

        detailsContainer.innerHTML = positions.map(pos => {
            const profitClass = pos.profit > 0 ? 'text-success' : pos.profit < 0 ? 'text-danger' : 'text-muted';
            const directionClass = pos.direction === 'LONG' ? 'text-success' : 'text-danger';
            const directionIcon = pos.direction === 'LONG' ? '📈' : '📉';

            return `
                <div class="position-item mb-3 p-2 border rounded">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="fw-bold">
                                ${directionIcon} <span class="${directionClass}">${pos.direction}</span>
                                <span class="text-muted">#${pos.id}</span>
                            </div>
                            <div class="small text-muted">
                                <strong>${pos.timeframe}</strong> • ${pos.volume} lots
                            </div>
                            <div class="small">
                                <span class="text-info">${pos.model_info}</span>
                            </div>
                            <div class="small text-muted">
                                Open: ${pos.price_open} → Current: ${pos.price_current}
                            </div>
                            <div class="small text-muted">
                                ${pos.time_open}
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="${profitClass} fw-bold">
                                ${pos.profit > 0 ? '+' : ''}$${pos.profit.toFixed(2)}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        // Add summary if multiple positions
        if (positions.length > 1) {
            const totalPnL = positions.reduce((sum, pos) => sum + pos.profit, 0);
            const totalPnLClass = totalPnL > 0 ? 'text-success' : totalPnL < 0 ? 'text-danger' : 'text-muted';

            detailsContainer.innerHTML += `
                <div class="border-top pt-2 mt-2">
                    <div class="d-flex justify-content-between">
                        <strong>Total P&L:</strong>
                        <strong class="${totalPnLClass}">
                            ${totalPnL > 0 ? '+' : ''}$${totalPnL.toFixed(2)}
                        </strong>
                    </div>
                </div>
            `;
        }
    }

    updateLiveTradingStatus(data) {
        const liveTrading = data.live_trading;

        if (liveTrading && liveTrading.status === 'ACTIVE') {
            // Show live trading section
            const liveTradingSection = document.getElementById('live-trading-section');
            if (liveTradingSection) {
                liveTradingSection.style.display = 'block';
            }

            // Update trades executed
            const tradesExecutedElement = document.getElementById('trades-executed');
            if (tradesExecutedElement) {
                tradesExecutedElement.textContent = liveTrading.trades_executed || 0;
            }

            // Update signals generated
            const signalsGeneratedElement = document.getElementById('signals-generated');
            if (signalsGeneratedElement) {
                signalsGeneratedElement.textContent = liveTrading.signals_generated || 0;
            }

            // Update last signal
            const lastSignalElement = document.getElementById('last-signal');
            if (lastSignalElement) {
                const lastSignal = liveTrading.last_signal;
                if (lastSignal) {
                    lastSignalElement.textContent = lastSignal.signal || '--';
                    lastSignalElement.className = `metric-value ${this.getSignalClass(lastSignal.signal)}`;
                } else {
                    lastSignalElement.textContent = '--';
                    lastSignalElement.className = 'metric-value';
                }
            }

            // Update risk status
            const riskStatusElement = document.getElementById('risk-status');
            if (riskStatusElement) {
                const riskStatus = liveTrading.risk_status || 'SAFE';
                riskStatusElement.textContent = riskStatus;
                riskStatusElement.className = `metric-value ${this.getRiskStatusClass(riskStatus)}`;
            }

            // Update last trade info
            const lastTradeInfo = document.getElementById('last-trade-info');
            const lastTradeDetails = document.getElementById('last-trade-details');
            if (lastTradeInfo && lastTradeDetails) {
                const lastTrade = liveTrading.last_trade;
                if (lastTrade) {
                    lastTradeDetails.textContent = `${lastTrade.action} ${lastTrade.volume} lots at ${lastTrade.price} (${lastTrade.timestamp})`;
                    lastTradeInfo.style.display = 'block';
                } else {
                    lastTradeInfo.style.display = 'none';
                }
            }

        } else {
            // Hide live trading section if not active
            const liveTradingSection = document.getElementById('live-trading-section');
            if (liveTradingSection) {
                liveTradingSection.style.display = 'none';
            }
        }
    }

    getSignalClass(signal) {
        if (signal === 'BUY' || signal === 1) return 'positive';
        if (signal === 'SELL' || signal === -1) return 'negative';
        return 'neutral';
    }

    getRiskStatusClass(status) {
        if (status === 'SAFE') return 'positive';
        if (status === 'WARNING') return 'neutral';
        if (status === 'DANGER') return 'negative';
        return 'neutral';
    }

    updatePriceCharts(data) {
        const priceData = data.price_data;
        if (!priceData) return;
        
        // Update charts for each timeframe
        Object.entries(priceData).forEach(([timeframe, chartData]) => {
            this.updateChart(timeframe, chartData);
        });
    }
    
    initializeCharts() {
        const timeframes = ['1M', '5M', '15M', '1H', '1D'];
        
        timeframes.forEach(tf => {
            const canvas = document.getElementById(`priceChart${tf}`);
            if (canvas) {
                const ctx = canvas.getContext('2d');
                
                this.charts[tf] = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: `DEX 900 DOWN ${tf}`,
                            data: [],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#c9d1d9'
                                }
                            }
                        },
                        scales: {
                            x: {
                                ticks: {
                                    color: '#8b949e'
                                },
                                grid: {
                                    color: '#30363d'
                                }
                            },
                            y: {
                                ticks: {
                                    color: '#8b949e'
                                },
                                grid: {
                                    color: '#30363d'
                                }
                            }
                        }
                    }
                });
            }
        });
    }
    
    updateChart(timeframe, chartData) {
        const chart = this.charts[timeframe];
        if (!chart || !chartData.timestamps || !chartData.close) return;
        
        chart.data.labels = chartData.timestamps;
        chart.data.datasets[0].data = chartData.close;
        chart.update('none'); // No animation for real-time updates
    }
    
    updateLastUpdateTime() {
        const updateElement = document.getElementById('last-update');
        if (updateElement && this.lastUpdate) {
            updateElement.textContent = this.lastUpdate.toLocaleTimeString();
        }
    }
    
    getStatusClass(status) {
        if (!status) return 'status-unknown';
        
        const statusStr = status.toString().toLowerCase();
        if (statusStr.includes('connected') || statusStr.includes('active') || statusStr.includes('operational')) {
            return 'status-operational';
        } else if (statusStr.includes('error') || statusStr.includes('failed')) {
            return 'status-error';
        } else if (statusStr.includes('warning') || statusStr.includes('inactive')) {
            return 'status-warning';
        } else {
            return 'status-unknown';
        }
    }
    
    showError(message) {
        console.error('Dashboard Error:', message);
        // Could add toast notifications here
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.dashboard = new TradingDashboard();
});

// Handle page visibility changes to pause/resume updates
document.addEventListener('visibilitychange', function() {
    if (window.dashboard) {
        if (document.hidden) {
            console.log('📱 Page hidden, dashboard updates continue in background');
        } else {
            console.log('👁️ Page visible, forcing dashboard update');
            window.dashboard.updateDashboard();
        }
    }
});
