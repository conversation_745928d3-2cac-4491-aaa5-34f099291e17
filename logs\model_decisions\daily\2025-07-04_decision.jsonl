{"timestamp": "2025-07-04T09:06:47.253474", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.551546037197113, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-04T09:06:47.315078", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.47189426855319655, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-04T09:06:47.319098", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-04T09:06:47.527120", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7722230553627014, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-04T09:06:47.587590", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6442684128698407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-04T09:06:47.597617", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99996018409729, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-04T09:06:47.706792", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999979734420776, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-04T09:06:47.767333", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.715, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-04T09:06:47.772373", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
