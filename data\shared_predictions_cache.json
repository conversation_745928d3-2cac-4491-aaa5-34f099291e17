{"timestamp": "2025-07-02T08:24:54.802860", "predictions": {"short_term_pattern_nn": {"signal": 2, "confidence": 0.8085024356842041, "model_type": "lstm", "timestamp": "08:24:54"}, "short_term_momentum_rf": {"signal": 0, "confidence": 0.9822321428571428, "model_type": "random_forest", "timestamp": "08:24:54"}, "short_term_reversion_gb": {"signal": 1, "confidence": 0.9999998524274585, "model_type": "gradient_boosting", "timestamp": "08:24:54"}, "medium_term_trend_lstm": {"signal": 0, "confidence": 0.8408039212226868, "model_type": "lstm", "timestamp": "08:24:54"}, "medium_term_breakout_rf": {"signal": 2, "confidence": 0.8426629864055777, "model_type": "random_forest", "timestamp": "08:24:54"}, "medium_term_volatility_xgb": {"signal": 0, "confidence": 0.999972939491272, "model_type": "xgboost", "timestamp": "08:24:54"}, "long_term_macro_dnn": {"signal": 0, "confidence": 0.9999992847442627, "model_type": "deep_nn", "timestamp": "08:24:54"}, "long_term_levels_rf": {"signal": 1, "confidence": 0.715, "model_type": "random_forest", "timestamp": "08:24:54"}, "long_term_portfolio_gb": {"signal": 0, "confidence": 0.9999998524274585, "model_type": "gradient_boosting", "timestamp": "08:24:54"}}, "ensemble_data": {"ensemble_signal": 1, "confidence": 0.9099081572511181, "consensus": "weak", "strong_signals": [{"model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8085024356842041, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, {"model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8426629864055777, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}], "timeframe_consensus_signals": []}}