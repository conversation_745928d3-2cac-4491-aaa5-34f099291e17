"""
Trading Signal Generator for Synthetic DEX 900 DOWN Index Trading System.
Converts AI model predictions into actionable trading signals with risk management.
"""

import logging
import numpy as np
import pandas as pd
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import config
from cross_model_synergy import CrossModelSynergy


# Set up logging
logger = logging.getLogger("TradingSignalGenerator")

class SignalType(Enum):
    """Trading signal types."""
    NO_SIGNAL = 0
    WEAK_BUY = 1
    STRONG_BUY = 2
    WEAK_SELL = -1
    STRONG_SELL = -2

class SignalConfidence(Enum):
    """Signal confidence levels."""
    VERY_LOW = 0.0
    LOW = 0.3
    MEDIUM = 0.5
    HIGH = 0.7
    VERY_HIGH = 0.9

@dataclass
class TradingSignal:
    """Trading signal with all necessary information."""
    signal_type: SignalType
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    risk_reward_ratio: float
    timeframe: int
    timestamp: datetime
    ai_predictions: Dict
    market_regime: str
    reasoning: str

class TradingSignalGenerator:
    """
    Generates trading signals from AI model predictions with comprehensive risk management.
    Optimized for DEX 900 DOWN Index synthetic trading.
    """
    
    def __init__(self, ai_manager, pattern_detector, data_collector, trading_engine=None):
        """Initialize the Trading Signal Generator."""
        self.ai_manager = ai_manager
        self.pattern_detector = pattern_detector
        self.data_collector = data_collector
        self._trading_engine = trading_engine  # Reference to trading engine for direct execution
        
        # Signal generation parameters
        self.min_confidence = config.MIN_SIGNAL_CONFIDENCE
        self.min_consensus_strength = 0.4  # Require 40% model agreement (AGGRESSIVE)
        self.max_position_size = config.MAX_POSITION_SIZE
        self.base_risk_per_trade = config.BASE_RISK_PER_TRADE
        
        # Risk management parameters (REMOVED DAILY TRADE LIMITS)
        # self.max_daily_trades = config.MAX_DAILY_TRADES  # REMOVED: No daily trade limits
        self.max_concurrent_positions = config.MAX_CONCURRENT_POSITIONS
        self.min_risk_reward = config.MIN_RISK_REWARD_RATIO
        
        # Synthetic-specific parameters
        self.jump_detection_threshold = 0.005  # 0.5% for jump detection
        self.volatility_expansion_threshold = 0.01  # 1% for volatility expansion
        self.regime_confidence_multiplier = {
            'QUIET': 0.8,      # Lower confidence in quiet periods
            'PRE_JUMP': 1.2,   # Higher confidence before jumps
            'JUMPING': 1.5,    # Highest confidence during jumps
            'POST_JUMP': 1.0,  # Normal confidence after jumps
            'REVERTING': 0.9   # Slightly lower confidence during reversion
        }
        
        # Signal tracking
        self.recent_signals = []
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()

        # Initialize Cross-Model Synergy System
        self.synergy_system = CrossModelSynergy()







        logger.info("Trading Signal Generator initialized with Cross-Model Synergy")





    def generate_signal(self, current_price: float, timeframe: int = 15) -> Optional[TradingSignal]:
        """Generate a trading signal based on current market conditions."""
        try:
            # Reset daily counters if new day
            self._reset_daily_counters()
            
            # Check if we can trade
            if not self._can_generate_signal():
                return None
                
            # Get latest market data
            market_data = self.data_collector.get_latest_data(timeframe, count=100)
            if market_data.empty:
                logger.warning("No market data available for signal generation")
                return None
                
            # Calculate synthetic indicators
            df_indicators = self.pattern_detector.calculate_synthetic_indicators(market_data)
            latest_data = df_indicators.iloc[-1]


            
            # Get AI ensemble prediction
            model_features = self._extract_features_for_ai(df_indicators)
            if not model_features:
                logger.warning("Could not extract features for AI prediction")
                return None

            # Use timeframe-specific ensemble voting (5-minute target for current trading cycle)
            ensemble_prediction = self.ai_manager.get_ensemble_prediction_with_features(model_features, target_timeframe=5)

            # CROSS-MODEL SYNERGY ANALYSIS - Enhance existing ensemble with synergy insights
            synergy_analysis = self.synergy_system.analyze_cross_timeframe_synergy(
                predictions=ensemble_prediction.get('predictions', {}),
                market_data=df_indicators
            )

            # Log synergy insights
            logger.info(f"SYNERGY ANALYSIS: Agreement={synergy_analysis.agreement_score:.3f}, "
                       f"Action={synergy_analysis.recommended_action}, "
                       f"Position Multiplier={synergy_analysis.position_multiplier:.2f}")

            for insight in synergy_analysis.synergy_insights:
                logger.info(f"SYNERGY INSIGHT: {insight}")

            # SAVE PREDICTIONS TO SHARED CACHE for dashboard synchronization
            self._save_predictions_to_shared_cache(model_features, ensemble_prediction)

            # Import decision logger
            from model_decision_logger import decision_logger

            # Check for strong signals that should trigger immediate trades
            strong_signals = ensemble_prediction.get('strong_signals', [])  # Ultra-strong (±2)
            timeframe_consensus_signals = ensemble_prediction.get('timeframe_consensus_signals', [])  # Consensus (±1)

            # CONFIDENCE-BASED TRADING LOGIC - PROCESS ALL SIGNALS
            signals_processed = 0
            first_signal = None  # Initialize for backward compatibility

            if strong_signals or timeframe_consensus_signals:
                # Strong signal (2/-2) detected - process ALL strong signals
                for strong_signal in strong_signals:
                    logger.info(f"STRONG SIGNAL DETECTED: {strong_signal['model_name']} - Signal: {strong_signal['signal']} (Confidence: {strong_signal['confidence']:.3f})")

                    # Create immediate trading signal based on ULTRA-STRONG model prediction (±2 only)
                    if strong_signal['signal'] == 2:
                        signal_type = SignalType.STRONG_BUY
                    elif strong_signal['signal'] == -2:
                        signal_type = SignalType.STRONG_SELL
                    else:
                        # This should never happen as strong_signals should only contain ±2
                        logger.error(f"Invalid strong signal strength: {strong_signal['signal']} (expected ±2)")
                        continue

                    # Calculate position size and risk management with synergy enhancement
                    regime_analysis = self._analyze_market_regime(latest_data, df_indicators)
                    base_position_size = self._calculate_position_size(strong_signal['confidence'], regime_analysis)

                    # Apply synergy position multiplier
                    position_size = base_position_size * synergy_analysis.position_multiplier

                    # Apply synergy confidence adjustment
                    adjusted_confidence = min(1.0, strong_signal['confidence'] + synergy_analysis.confidence_adjustment)

                    # Determine timeframe from the strong signal's timeframe category
                    timeframe_group = self._map_timeframe_category_to_group(strong_signal['timeframe_category'])
                    stop_loss, take_profit = self._calculate_stop_loss_take_profit(signal_type, current_price, regime_analysis, df_indicators, timeframe_group)

                    # Apply synergy risk adjustment to stop loss
                    stop_loss, take_profit = self._apply_synergy_risk_adjustment(
                        signal_type, current_price, stop_loss, take_profit, synergy_analysis.risk_adjustment
                    )

                    # Calculate risk-reward ratio
                    if signal_type == SignalType.STRONG_BUY:
                        risk_reward = (take_profit - current_price) / (current_price - stop_loss)
                    else:  # STRONG_SELL
                        risk_reward = (current_price - take_profit) / (stop_loss - current_price)

                    # Create trading signal
                    trading_signal = TradingSignal(
                        signal_type=signal_type,
                        confidence=strong_signal['confidence'],
                        entry_price=current_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        position_size=position_size,
                        risk_reward_ratio=risk_reward,
                        timeframe=5,  # 5-minute timeframe for strong signals
                        timestamp=pd.Timestamp.now(),
                        ai_predictions=ensemble_prediction,
                        market_regime=str(regime_analysis),
                        reasoning=f"Strong signal ({strong_signal['signal']}) from {strong_signal['model_name']} ({strong_signal['timeframe_category']}) with {strong_signal['confidence']:.3f} confidence. Pattern: {strong_signal['purpose']}"
                    )

                    # Log trade trigger
                    decision_logger.log_trade_trigger({
                        "trigger_type": "STRONG_SIGNAL",
                        "triggering_model": strong_signal['model_name'],
                        "signal": strong_signal['signal'],
                        "confidence": strong_signal['confidence'],
                        "entry_price": current_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "position_size": position_size,
                        "reason": f"Strong signal ({strong_signal['signal']}) from {strong_signal['model_name']} with {strong_signal['confidence']:.3f} confidence"
                    })

                    logger.info(f"IMMEDIATE TRADE SIGNAL GENERATED: {signal_type.name}")

                    # EXECUTE SIGNAL IMMEDIATELY - Don't return, process all signals
                    try:
                        # Import trading engine to execute signal directly
                        if hasattr(self, '_trading_engine') and self._trading_engine:
                            self._trading_engine._execute_signal(trading_signal)
                            signals_processed += 1
                        else:
                            # Fallback: Store first signal if no direct execution available
                            if first_signal is None:
                                first_signal = trading_signal
                            signals_processed += 1
                    except Exception as e:
                        logger.error(f"Error executing strong signal: {e}")
                        # Continue processing other signals

                # If we processed strong signals and have direct execution, return None (signals already executed)
                if signals_processed > 0 and hasattr(self, '_trading_engine') and self._trading_engine:
                    logger.info(f"Processed {signals_processed} strong signals directly")
                    return None

                # If no direct execution available, return the first signal for backward compatibility
                if signals_processed > 0 and first_signal is not None:
                    return first_signal

                # Check timeframe consensus signals (±1 with confidence ≥0.7, need 2+ models)
                for consensus_signal in timeframe_consensus_signals:
                    logger.info(f"🤝 TIMEFRAME CONSENSUS DETECTED: {consensus_signal['model_name']} - Signal: {consensus_signal['signal']} (Confidence: {consensus_signal['confidence']:.3f})")

                    # Create immediate trading signal based on timeframe consensus (±1 only)
                    if consensus_signal['signal'] == 1:
                        signal_type = SignalType.WEAK_BUY
                    elif consensus_signal['signal'] == -1:
                        signal_type = SignalType.WEAK_SELL
                    else:
                        # This should never happen as consensus_signals should only contain ±1
                        logger.error(f"Invalid consensus signal strength: {consensus_signal['signal']} (expected ±1)")
                        continue

                    # Calculate position size and risk management
                    regime_analysis = self._analyze_market_regime(latest_data, df_indicators)
                    position_size = self._calculate_position_size(consensus_signal['confidence'], regime_analysis)

                    # Determine timeframe from the consensus signal's timeframe category
                    timeframe_group = self._map_timeframe_category_to_group(consensus_signal['timeframe_category'])
                    stop_loss, take_profit = self._calculate_stop_loss_take_profit(signal_type, current_price, regime_analysis, df_indicators, timeframe_group)

                    # Calculate risk-reward ratio
                    if signal_type == SignalType.WEAK_BUY:
                        risk_reward = (take_profit - current_price) / (current_price - stop_loss)
                    else:  # WEAK_SELL
                        risk_reward = (current_price - take_profit) / (stop_loss - current_price)

                    # Create trading signal
                    trading_signal = TradingSignal(
                        signal_type=signal_type,
                        confidence=consensus_signal['confidence'],
                        entry_price=current_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        position_size=position_size,
                        risk_reward_ratio=risk_reward,
                        timeframe=5,  # 5-minute timeframe for consensus signals
                        timestamp=pd.Timestamp.now(),
                        ai_predictions=ensemble_prediction,
                        market_regime=str(regime_analysis),
                        reasoning=f"Timeframe consensus signal ({consensus_signal['signal']}) from {consensus_signal['timeframe_category']} models with {consensus_signal['confidence']:.3f} confidence. Pattern: {consensus_signal['purpose']}"
                    )

                    # Log trade trigger
                    decision_logger.log_trade_trigger({
                        "trigger_type": "TIMEFRAME_CONSENSUS",
                        "triggering_model": consensus_signal['model_name'],
                        "signal": consensus_signal['signal'],
                        "confidence": consensus_signal['confidence'],
                        "entry_price": current_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "position_size": position_size,
                        "reason": f"Timeframe consensus signal ({consensus_signal['signal']}) from {consensus_signal['timeframe_category']} models with {consensus_signal['confidence']:.3f} confidence"
                    })

                    logger.info(f"✅ TIMEFRAME CONSENSUS TRADE SIGNAL GENERATED: {signal_type.name}")
                    return trading_signal

            # Analyze market regime for ensemble-based decisions
            regime_analysis = self._analyze_market_regime(latest_data, df_indicators)

            # Generate signal based on ensemble prediction and market conditions (for weaker signals)
            signal = self._create_trading_signal(
                ensemble_prediction=ensemble_prediction,
                regime_analysis=regime_analysis,
                current_price=current_price,
                market_data=df_indicators,
                timeframe=timeframe
            )
            
            if signal:
                self._track_signal(signal)
                logger.info(f"Generated {signal.signal_type.name} signal with {signal.confidence:.3f} confidence")
                
            return signal
            
        except Exception as e:
            logger.error(f"Error generating trading signal: {e}")
            return None
            
    def _extract_features_for_ai(self, df_indicators: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Extract features for AI model prediction - returns features for each model."""
        try:
            if len(df_indicators) < 20:
                return {}

            latest_row = df_indicators.iloc[-1]

            if not self.ai_manager.scalers:
                logger.warning("No trained AI models available")
                return {}

            # Extract features for each trained model
            model_features = {}

            for model_name in self.ai_manager.scalers.keys():
                try:
                    features = self._extract_features_for_model(df_indicators, model_name, latest_row)
                    if features is not None:
                        model_features[model_name] = features
                except Exception as e:
                    logger.error(f"Error extracting features for {model_name}: {e}")

            return model_features

        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return {}

    def _extract_features_for_model(self, df_indicators: pd.DataFrame, model_name: str, latest_row: pd.Series) -> Optional[np.ndarray]:
        """Extract features for a specific model based on its configuration."""
        try:
            # Get model configuration
            model_config = self.ai_manager.model_configs.get(model_name, {})
            feature_types = model_config.get("features", [])
            required_features = self.ai_manager.scalers[model_name].n_features_in_

            features = []

            # Extract features based on model's feature types
            if "price_action" in feature_types:
                features.extend(self._get_price_action_features(df_indicators, latest_row))

            if "volatility_regime" in feature_types:
                features.extend(self._get_volatility_regime_features(df_indicators, latest_row))

            if "micro_patterns" in feature_types:
                features.extend(self._get_micro_pattern_features(df_indicators, latest_row))

            if "momentum" in feature_types:
                features.extend(self._get_momentum_features(df_indicators, latest_row))

            if "rsi" in feature_types:
                features.extend(self._get_rsi_features(df_indicators, latest_row))

            if "macd" in feature_types:
                features.extend(self._get_macd_features(df_indicators, latest_row))

            if "volatility_adjusted_momentum" in feature_types:
                features.extend(self._get_volatility_adjusted_momentum_features(df_indicators, latest_row))

            if "deviation" in feature_types:
                features.extend(self._get_deviation_features(df_indicators, latest_row))

            if "bollinger" in feature_types:
                features.extend(self._get_bollinger_features(df_indicators, latest_row))

            if "rsi_extreme" in feature_types:
                features.extend(self._get_rsi_extreme_features(df_indicators, latest_row))

            if "trend_strength" in feature_types:
                features.extend(self._get_trend_strength_features(df_indicators, latest_row))

            if "moving_averages" in feature_types:
                features.extend(self._get_moving_average_features(df_indicators, latest_row))

            if "trend_sustainability" in feature_types:
                features.extend(self._get_trend_sustainability_features(df_indicators, latest_row))

            if "support_resistance" in feature_types:
                features.extend(self._get_support_resistance_features(df_indicators, latest_row))

            if "false_breakout_filter" in feature_types:
                features.extend(self._get_false_breakout_filter_features(df_indicators, latest_row))

            if "volatility" in feature_types:
                features.extend(self._get_volatility_features(df_indicators, latest_row))

            if "volatility_regime" in feature_types:
                features.extend(self._get_volatility_regime_features(df_indicators, latest_row))

            if "atr" in feature_types:
                features.extend(self._get_atr_features(df_indicators, latest_row))

            if "bollinger_width" in feature_types:
                features.extend(self._get_bollinger_width_features(df_indicators, latest_row))

            # Add trend direction features
            if "short_term_trend_direction" in feature_types:
                features.extend(self._get_short_term_trend_direction_features(df_indicators, latest_row))

            if "medium_term_trend_direction" in feature_types:
                features.extend(self._get_medium_term_trend_direction_features(df_indicators, latest_row))

            if "long_term_trend_direction" in feature_types:
                features.extend(self._get_long_term_trend_direction_features(df_indicators, latest_row))

            # Add alignment features
            if "trend_alignment" in feature_types:
                features.extend(self._get_trend_alignment_features(df_indicators, latest_row))

            if "momentum_trend_alignment" in feature_types:
                features.extend(self._get_momentum_trend_alignment_features(df_indicators, latest_row))

            if "reversion_trend_context" in feature_types:
                features.extend(self._get_reversion_trend_context_features(df_indicators, latest_row))

            if "cross_timeframe_alignment" in feature_types:
                features.extend(self._get_cross_timeframe_alignment_features(df_indicators, latest_row))

            if "breakout_trend_alignment" in feature_types:
                features.extend(self._get_breakout_trend_alignment_features(df_indicators, latest_row))

            if "volatility_trend_context" in feature_types:
                features.extend(self._get_volatility_trend_context_features(df_indicators, latest_row))

            if "macro_trend_alignment" in feature_types:
                features.extend(self._get_macro_trend_alignment_features(df_indicators, latest_row))

            if "level_trend_context" in feature_types:
                features.extend(self._get_level_trend_context_features(df_indicators, latest_row))

            if "portfolio_trend_alignment" in feature_types:
                features.extend(self._get_portfolio_trend_alignment_features(df_indicators, latest_row))

            # Always add synthetic-specific features
            features.extend(self._get_synthetic_features(df_indicators, latest_row))

            # IMPORTANT: Do NOT pad or trim features - they should match exactly
            # If there's a mismatch, it indicates a bug in feature extraction consistency
            if len(features) != required_features:
                logger.error(f"Feature count mismatch for {model_name}: got {len(features)}, expected {required_features}")
                logger.error(f"Feature types: {feature_types}")
                logger.error(f"Generated features: {len(features)} values")
                return None

            return np.array(features)

        except Exception as e:
            logger.error(f"Error extracting features for model {model_name}: {e}")
            return None

    def _get_price_action_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract price action features."""
        features = []
        if len(df_indicators) >= 10:
            features.extend([
                latest_row['close'] / df_indicators['close'].iloc[-10] - 1,  # 10-period return
                latest_row['high'] / latest_row['close'] - 1,               # High deviation
                latest_row['low'] / latest_row['close'] - 1,                # Low deviation
                df_indicators['volume'].iloc[-5:].mean(),                   # Recent volume
                len(df_indicators[df_indicators['close'] > df_indicators['open']]) / len(df_indicators)  # Bullish ratio
            ])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0, 0.5])
        return features

    def _get_key_levels_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract key levels features"""
        features = []
        
        if len(df_indicators) >= 50:
            # Support and resistance levels
            high_50 = df_indicators['high'].rolling(50).max().iloc[-1]
            low_50 = df_indicators['low'].rolling(50).min().iloc[-1]
            
            # Distance to key levels
            resistance_distance = (high_50 - latest_row['close']) / latest_row['close']
            support_distance = (latest_row['close'] - low_50) / latest_row['close']
            
            features.extend([resistance_distance, support_distance])
            
            # Level strength (how often price bounced off these levels)
            resistance_touches = ((df_indicators['high'].iloc[-20:] >= high_50 * 0.995) & 
                                (df_indicators['high'].iloc[-20:] <= high_50 * 1.005)).sum()
            support_touches = ((df_indicators['low'].iloc[-20:] >= low_50 * 0.995) & 
                             (df_indicators['low'].iloc[-20:] <= low_50 * 1.005)).sum()
            
            features.extend([resistance_touches / 20, support_touches / 20])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
            
        return features

    def _get_institutional_zones_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract institutional zones features"""
        features = []
        
        if len(df_indicators) >= 100:
            # Volume-weighted price levels (proxy for institutional activity)
            volume_weighted_price = (df_indicators['close'] * df_indicators.get('volume', 1)).rolling(20).sum() / df_indicators.get('volume', 1).rolling(20).sum()
            vwap_distance = (latest_row['close'] - volume_weighted_price.iloc[-1]) / latest_row['close']
            features.append(vwap_distance if not np.isnan(vwap_distance) else 0.0)
            
            # High volume zones (institutional accumulation/distribution)
            avg_volume = df_indicators.get('volume', pd.Series([1] * len(df_indicators))).rolling(50).mean().iloc[-1]
            recent_volume = df_indicators.get('volume', pd.Series([1] * len(df_indicators))).iloc[-1]
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
            features.append(volume_ratio)
            
            # Price consolidation zones (where institutions accumulate)
            price_range = df_indicators['high'].rolling(20).max() - df_indicators['low'].rolling(20).min()
            consolidation_strength = 1.0 / (price_range.iloc[-1] / latest_row['close'] + 1e-8)
            features.append(consolidation_strength if not np.isnan(consolidation_strength) else 0.0)
        else:
            features.extend([0.0, 1.0, 0.0])
            
        return features

    def _get_fibonacci_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Fibonacci retracement features"""
        features = []
        
        if len(df_indicators) >= 50:
            # Find recent swing high and low
            swing_high = df_indicators['high'].rolling(20).max().iloc[-1]
            swing_low = df_indicators['low'].rolling(20).min().iloc[-1]
            
            # Calculate Fibonacci levels
            fib_range = swing_high - swing_low
            fib_levels = {
                '23.6': swing_high - 0.236 * fib_range,
                '38.2': swing_high - 0.382 * fib_range,
                '50.0': swing_high - 0.500 * fib_range,
                '61.8': swing_high - 0.618 * fib_range,
                '78.6': swing_high - 0.786 * fib_range
            }
            
            # Distance to nearest Fibonacci level
            distances = [abs(latest_row['close'] - level) / latest_row['close'] for level in fib_levels.values()]
            nearest_fib_distance = min(distances)
            features.append(nearest_fib_distance)
            
            # Which Fibonacci zone we're in (normalized)
            if fib_range > 0:
                fib_position = (latest_row['close'] - swing_low) / fib_range
                features.append(max(0.0, min(1.0, fib_position)))
            else:
                features.append(0.5)
                
            # Fibonacci confluence (multiple timeframes)
            if len(df_indicators) >= 100:
                # Longer-term Fibonacci levels
                long_swing_high = df_indicators['high'].rolling(50).max().iloc[-1]
                long_swing_low = df_indicators['low'].rolling(50).min().iloc[-1]
                long_fib_range = long_swing_high - long_swing_low
                
                if long_fib_range > 0:
                    long_fib_position = (latest_row['close'] - long_swing_low) / long_fib_range
                    confluence = abs(fib_position - long_fib_position)
                    features.append(confluence)
                else:
                    features.append(0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.5, 0.0])
            
        return features

    def _get_volatility_regime_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility regime features (replaces volume features)."""
        features = []

        if len(df_indicators) >= 20:
            # ATR normalized by price
            high_low_diff = df_indicators['high'] - df_indicators['low']
            atr = high_low_diff.rolling(14).mean()
            atr_normalized = atr.iloc[-1] / latest_row['close'] if latest_row['close'] > 0 else 0.0
            features.append(atr_normalized)

            # Bollinger Bandwidth
            bb_mean = df_indicators['close'].rolling(20).mean()
            bb_std = df_indicators['close'].rolling(20).std()
            bb_upper = bb_mean + 2 * bb_std
            bb_lower = bb_mean - 2 * bb_std
            bb_bandwidth = (bb_upper.iloc[-1] - bb_lower.iloc[-1]) / bb_mean.iloc[-1] if bb_mean.iloc[-1] > 0 else 0.0
            features.append(bb_bandwidth)

            # Volatility percentile (current vs historical)
            volatility = df_indicators['close'].rolling(5).std()
            vol_q20 = volatility.quantile(0.2)
            vol_q80 = volatility.quantile(0.8)
            if vol_q80 > vol_q20:
                vol_percentile = (volatility.iloc[-1] - vol_q20) / (vol_q80 - vol_q20)
                vol_percentile = max(0, min(1, vol_percentile))
            else:
                vol_percentile = 0.5
            features.append(vol_percentile)
        else:
            features.extend([0.0, 0.0, 0.5])

        return features

    def _get_micro_pattern_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract micro pattern features."""
        features = []
        if len(df_indicators) >= 5:
            # Recent price patterns
            recent_highs = df_indicators['high'].iloc[-5:].max()
            recent_lows = df_indicators['low'].iloc[-5:].min()
            features.extend([
                (latest_row['close'] - recent_lows) / (recent_highs - recent_lows) if recent_highs != recent_lows else 0.5,
                (latest_row['high'] - latest_row['low']) / latest_row['close']
            ])
        else:
            features.extend([0.5, 0.0])
        return features

    def _get_momentum_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract momentum features."""
        features = []
        if len(df_indicators) >= 5:
            features.extend([
                latest_row['close'] / df_indicators['close'].iloc[-5] - 1,  # 5-period momentum
                df_indicators['close'].iloc[-3:].mean() / df_indicators['close'].iloc[-6:-3].mean() - 1 if len(df_indicators) >= 6 else 0.0
            ])
        else:
            features.extend([0.0, 0.0])
        return features

    def _get_rsi_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract RSI-like features."""
        features = []
        if len(df_indicators) >= 14:
            returns = df_indicators['close'].pct_change().dropna()
            gains = returns.where(returns > 0, 0)
            losses = -returns.where(returns < 0, 0)
            avg_gain = gains.rolling(14).mean().iloc[-1]
            avg_loss = losses.rolling(14).mean().iloc[-1]
            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 100 if avg_gain > 0 else 50
            features.append(rsi / 100)  # Normalize to 0-1
        else:
            features.append(0.5)
        return features

    def _get_macd_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract MACD-like features."""
        features = []
        if len(df_indicators) >= 26:
            ema12 = df_indicators['close'].ewm(span=12).mean().iloc[-1]
            ema26 = df_indicators['close'].ewm(span=26).mean().iloc[-1]
            macd = (ema12 - ema26) / latest_row['close']
            features.append(macd)
        else:
            features.append(0.0)
        return features

    def _get_volatility_adjusted_momentum_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility-adjusted momentum features (replaces volume_velocity)."""
        features = []

        if len(df_indicators) >= 20:
            # Calculate ATR threshold
            high_low_diff = df_indicators['high'] - df_indicators['low']
            atr = high_low_diff.rolling(14).mean()
            atr_threshold = atr.rolling(20).median().iloc[-1]
            current_atr = atr.iloc[-1]

            # Momentum only when ATR is above threshold
            if current_atr > atr_threshold and not pd.isna(current_atr) and not pd.isna(atr_threshold):
                momentum_5 = latest_row['close'] / df_indicators['close'].iloc[-5] - 1 if len(df_indicators) >= 5 else 0.0
                features.append(momentum_5)
            else:
                features.append(0.0)  # No momentum signal in low volatility

            # RSI adjusted for volatility
            rsi = self._calculate_rsi_simple(df_indicators['close'])
            vol_adjustment = min(2.0, current_atr / atr_threshold) if atr_threshold > 0 else 1.0
            rsi_adjusted = rsi * vol_adjustment
            features.append(rsi_adjusted / 100)

            # MACD filtered by ATR
            if len(df_indicators) >= 26:
                ema12 = df_indicators['close'].ewm(span=12).mean().iloc[-1]
                ema26 = df_indicators['close'].ewm(span=26).mean().iloc[-1]
                macd = (ema12 - ema26) / latest_row['close'] if latest_row['close'] > 0 else 0.0

                if current_atr > atr_threshold * 0.8:  # Only use MACD in sufficient volatility
                    features.append(macd)
                else:
                    features.append(0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.5, 0.0])

        return features

    def _calculate_rsi_simple(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate simple RSI."""
        if len(prices) < period + 1:
            return 50.0

        delta = prices.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(window=period).mean().iloc[-1]
        avg_loss = loss.rolling(window=period).mean().iloc[-1]

        if avg_loss == 0:
            return 100.0 if avg_gain > 0 else 50.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi if not pd.isna(rsi) else 50.0

    def _get_deviation_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract deviation features."""
        features = []
        if len(df_indicators) >= 20:
            mean_price = df_indicators['close'].rolling(20).mean().iloc[-1]
            std_price = df_indicators['close'].rolling(20).std().iloc[-1]
            deviation = (latest_row['close'] - mean_price) / std_price if std_price > 0 else 0
            features.append(deviation)
        else:
            features.append(0.0)
        return features

    def _get_bollinger_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Bollinger Band features with backward compatibility and dynamic adaptation."""
        features = []
        if len(df_indicators) >= 20:
            # Get volatility regime for dynamic threshold adjustment
            volatility_regime = self._detect_volatility_regime(df_indicators)

            # Dynamic Bollinger parameters based on volatility regime
            bb_period, bb_multiplier = self._get_dynamic_bollinger_params(volatility_regime)

            # Calculate dynamic Bollinger Bands
            mean_price = df_indicators['close'].rolling(bb_period).mean().iloc[-1]
            std_price = df_indicators['close'].rolling(bb_period).std().iloc[-1]

            # Adaptive multiplier based on synthetic index behavior
            adaptive_multiplier = self._calculate_adaptive_multiplier(df_indicators, bb_multiplier, volatility_regime)

            upper_band = mean_price + (adaptive_multiplier * std_price)
            lower_band = mean_price - (adaptive_multiplier * std_price)

            # Enhanced position calculation with regime awareness
            if upper_band != lower_band:
                bb_position = (latest_row['close'] - lower_band) / (upper_band - lower_band)

                # Regime-adjusted position (more sensitive in certain regimes)
                bb_position = self._adjust_bb_position_for_regime(bb_position, volatility_regime)
            else:
                bb_position = 0.5

            features.append(bb_position)

            # BACKWARD COMPATIBILITY: Only add extra features for models that expect them
            # The old short_term_reversion_gb model expects only 1 Bollinger feature
            # New models or retrained models can use the full dynamic feature set

        else:
            features.append(0.5)  # Default bb_position only for backward compatibility
        return features

    def _get_dynamic_bollinger_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Enhanced Dynamic Bollinger Band features (for new/retrained models)."""
        features = []
        if len(df_indicators) >= 20:
            # Get volatility regime for dynamic threshold adjustment
            volatility_regime = self._detect_volatility_regime(df_indicators)

            # Dynamic Bollinger parameters based on volatility regime
            bb_period, bb_multiplier = self._get_dynamic_bollinger_params(volatility_regime)

            # Calculate dynamic Bollinger Bands
            mean_price = df_indicators['close'].rolling(bb_period).mean().iloc[-1]
            std_price = df_indicators['close'].rolling(bb_period).std().iloc[-1]

            # Adaptive multiplier based on synthetic index behavior
            adaptive_multiplier = self._calculate_adaptive_multiplier(df_indicators, bb_multiplier, volatility_regime)

            upper_band = mean_price + (adaptive_multiplier * std_price)
            lower_band = mean_price - (adaptive_multiplier * std_price)

            # Enhanced position calculation with regime awareness
            if upper_band != lower_band:
                bb_position = (latest_row['close'] - lower_band) / (upper_band - lower_band)

                # Regime-adjusted position (more sensitive in certain regimes)
                bb_position = self._adjust_bb_position_for_regime(bb_position, volatility_regime)
            else:
                bb_position = 0.5

            features.append(bb_position)

            # Add additional dynamic features for enhanced models
            features.extend(self._get_dynamic_bollinger_extras(df_indicators, mean_price, std_price, adaptive_multiplier))
        else:
            features.extend([0.5, 0.0, 0.0])  # bb_position, squeeze_ratio, expansion_rate
        return features

    def _detect_volatility_regime(self, df_indicators: pd.DataFrame) -> str:
        """Detect current volatility regime for dynamic Bollinger adaptation."""
        try:
            if len(df_indicators) < 50:
                return "NORMAL"

            # Calculate recent volatility metrics
            returns = df_indicators['close'].pct_change().fillna(0)
            recent_vol = returns.rolling(20).std().iloc[-1]
            long_vol = returns.rolling(50).std().iloc[-1]

            # ATR-based volatility
            high_low = df_indicators['high'] - df_indicators['low']
            atr_recent = high_low.rolling(14).mean().iloc[-1]
            atr_long = high_low.rolling(50).mean().iloc[-1]

            # Regime classification
            vol_ratio = recent_vol / long_vol if long_vol > 0 else 1.0
            atr_ratio = atr_recent / atr_long if atr_long > 0 else 1.0

            if vol_ratio > 1.5 or atr_ratio > 1.3:
                return "HIGH_VOLATILITY"
            elif vol_ratio < 0.7 or atr_ratio < 0.8:
                return "LOW_VOLATILITY"
            else:
                return "NORMAL"

        except Exception as e:
            logger.warning(f"Error detecting volatility regime: {e}")
            return "NORMAL"

    def _get_dynamic_bollinger_params(self, volatility_regime: str) -> tuple:
        """Get dynamic Bollinger Band parameters based on volatility regime."""
        # Regime-specific parameters optimized for DEX 900 DOWN synthetic index
        regime_params = {
            "HIGH_VOLATILITY": (15, 2.5),  # Shorter period, wider bands
            "LOW_VOLATILITY": (25, 1.8),   # Longer period, tighter bands
            "NORMAL": (20, 2.0)            # Standard parameters
        }

        return regime_params.get(volatility_regime, (20, 2.0))

    def _calculate_adaptive_multiplier(self, df_indicators: pd.DataFrame, base_multiplier: float, volatility_regime: str) -> float:
        """Calculate adaptive multiplier for Bollinger Bands based on synthetic index behavior."""
        try:
            if len(df_indicators) < 20:
                return base_multiplier

            # Analyze recent price behavior for synthetic index patterns
            returns = df_indicators['close'].pct_change().fillna(0)
            recent_returns = returns.tail(10)

            # Check for synthetic index "jump" patterns
            large_moves = (abs(recent_returns) > 0.002).sum()  # 0.2% moves
            consecutive_moves = self._count_consecutive_moves(recent_returns)

            # Adaptive adjustment based on synthetic behavior
            adjustment = 1.0

            # More large moves = wider bands (expect more volatility)
            if large_moves >= 3:
                adjustment += 0.2
            elif large_moves <= 1:
                adjustment -= 0.1

            # Consecutive moves in same direction = potential trend (tighter bands)
            if consecutive_moves >= 3:
                adjustment -= 0.15

            # Regime-specific fine-tuning
            if volatility_regime == "HIGH_VOLATILITY":
                adjustment += 0.1  # Even wider in high vol
            elif volatility_regime == "LOW_VOLATILITY":
                adjustment -= 0.05  # Slightly tighter in low vol

            # Ensure reasonable bounds
            final_multiplier = base_multiplier * max(0.8, min(1.5, adjustment))
            return final_multiplier

        except Exception as e:
            logger.warning(f"Error calculating adaptive multiplier: {e}")
            return base_multiplier

    def _count_consecutive_moves(self, returns: pd.Series) -> int:
        """Count consecutive moves in the same direction."""
        try:
            if len(returns) < 2:
                return 0

            # Convert to direction signals
            directions = []
            for ret in returns:
                if ret > 0.0005:  # Up move (0.05%)
                    directions.append(1)
                elif ret < -0.0005:  # Down move
                    directions.append(-1)
                else:
                    directions.append(0)  # Neutral

            # Count longest consecutive sequence
            max_consecutive = 0
            current_consecutive = 1

            for i in range(1, len(directions)):
                if directions[i] == directions[i-1] and directions[i] != 0:
                    current_consecutive += 1
                else:
                    max_consecutive = max(max_consecutive, current_consecutive)
                    current_consecutive = 1

            return max(max_consecutive, current_consecutive)

        except Exception as e:
            logger.warning(f"Error counting consecutive moves: {e}")
            return 0

    def _adjust_bb_position_for_regime(self, bb_position: float, volatility_regime: str) -> float:
        """Adjust Bollinger Band position based on volatility regime."""
        try:
            # Regime-specific sensitivity adjustments
            if volatility_regime == "HIGH_VOLATILITY":
                # Less sensitive in high volatility (avoid false signals)
                if bb_position > 0.8:
                    bb_position = 0.8 + (bb_position - 0.8) * 0.7
                elif bb_position < 0.2:
                    bb_position = 0.2 + (bb_position - 0.2) * 0.7

            elif volatility_regime == "LOW_VOLATILITY":
                # More sensitive in low volatility (catch subtle signals)
                if bb_position > 0.7:
                    bb_position = 0.7 + (bb_position - 0.7) * 1.3
                elif bb_position < 0.3:
                    bb_position = 0.3 + (bb_position - 0.3) * 1.3

            # Ensure bounds
            return max(0.0, min(1.0, bb_position))

        except Exception as e:
            logger.warning(f"Error adjusting BB position: {e}")
            return bb_position

    def _get_dynamic_bollinger_extras(self, df_indicators: pd.DataFrame, mean_price: float, std_price: float, multiplier: float) -> List[float]:
        """Get additional dynamic Bollinger Band features."""
        try:
            features = []

            if len(df_indicators) >= 50:
                # Bollinger Band squeeze ratio (current vs historical)
                historical_std = df_indicators['close'].rolling(50).std().mean()
                squeeze_ratio = std_price / historical_std if historical_std > 0 else 1.0
                features.append(squeeze_ratio)

                # Band expansion rate (how fast bands are expanding/contracting)
                recent_stds = df_indicators['close'].rolling(20).std().tail(5)
                if len(recent_stds) >= 2:
                    expansion_rate = (recent_stds.iloc[-1] - recent_stds.iloc[0]) / recent_stds.iloc[0] if recent_stds.iloc[0] > 0 else 0.0
                else:
                    expansion_rate = 0.0
                features.append(expansion_rate)
            else:
                features.extend([1.0, 0.0])  # Default values

            return features

        except Exception as e:
            logger.warning(f"Error calculating dynamic Bollinger extras: {e}")
            return [1.0, 0.0]

    def _apply_synergy_risk_adjustment(self, signal_type: SignalType, current_price: float,
                                     base_stop_loss: float, base_take_profit: float,
                                     risk_adjustment: float) -> Tuple[float, float]:
        """Apply synergy risk adjustment to stop loss and take profit levels."""
        try:
            # Calculate adjustment amounts
            if signal_type in [SignalType.STRONG_BUY, SignalType.WEAK_BUY]:
                # For BUY trades: adjust stop loss (below current price)
                sl_distance = current_price - base_stop_loss
                adjusted_sl_distance = sl_distance * (1 + risk_adjustment)
                adjusted_stop_loss = current_price - adjusted_sl_distance

                # Keep take profit unchanged for BUY trades
                adjusted_take_profit = base_take_profit

            else:  # SELL trades
                # For SELL trades: adjust stop loss (above current price)
                sl_distance = base_stop_loss - current_price
                adjusted_sl_distance = sl_distance * (1 + risk_adjustment)
                adjusted_stop_loss = current_price + adjusted_sl_distance

                # Keep take profit unchanged for SELL trades
                adjusted_take_profit = base_take_profit

            # Ensure reasonable bounds (don't adjust more than 50%)
            max_adjustment = 0.5
            if signal_type in [SignalType.STRONG_BUY, SignalType.WEAK_BUY]:
                min_stop_loss = current_price * (1 - max_adjustment)
                adjusted_stop_loss = max(adjusted_stop_loss, min_stop_loss)
            else:
                max_stop_loss = current_price * (1 + max_adjustment)
                adjusted_stop_loss = min(adjusted_stop_loss, max_stop_loss)

            return adjusted_stop_loss, adjusted_take_profit

        except Exception as e:
            logger.warning(f"Error applying synergy risk adjustment: {e}")
            return base_stop_loss, base_take_profit

    def _get_rsi_extreme_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract RSI extreme features."""
        rsi_features = self._get_rsi_features(df_indicators, latest_row)
        features = []
        if rsi_features:
            rsi = rsi_features[0] * 100  # Convert back to 0-100 scale
            extreme = 1.0 if rsi > 80 or rsi < 20 else 0.0
            features.append(extreme)
        else:
            features.append(0.0)
        return features

    def _get_trend_strength_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract trend strength features."""
        features = []
        if len(df_indicators) >= 20:
            ma_short = df_indicators['close'].rolling(5).mean().iloc[-1]
            ma_long = df_indicators['close'].rolling(20).mean().iloc[-1]
            trend_strength = (ma_short - ma_long) / latest_row['close']
            features.append(trend_strength)
        else:
            features.append(0.0)
        return features

    def _get_moving_average_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract moving average features."""
        features = []
        if len(df_indicators) >= 20:
            ma5 = df_indicators['close'].rolling(5).mean().iloc[-1]
            ma10 = df_indicators['close'].rolling(10).mean().iloc[-1]
            ma20 = df_indicators['close'].rolling(20).mean().iloc[-1]
            features.extend([
                latest_row['close'] / ma5 - 1,
                latest_row['close'] / ma10 - 1,
                latest_row['close'] / ma20 - 1
            ])
        else:
            features.extend([0.0, 0.0, 0.0])
        return features

    def _get_trend_sustainability_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract trend sustainability features (replaces volume_trend)."""
        features = []

        if len(df_indicators) >= 20:
            # ADX-like calculation for trend strength
            high_low = df_indicators['high'] - df_indicators['low']
            high_close = abs(df_indicators['high'] - df_indicators['close'].shift(1))
            low_close = abs(df_indicators['low'] - df_indicators['close'].shift(1))

            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean()

            # Directional movement
            plus_dm = (df_indicators['high'] - df_indicators['high'].shift(1)).where(
                (df_indicators['high'] - df_indicators['high'].shift(1)) >
                (df_indicators['low'].shift(1) - df_indicators['low']), 0)
            minus_dm = (df_indicators['low'].shift(1) - df_indicators['low']).where(
                (df_indicators['low'].shift(1) - df_indicators['low']) >
                (df_indicators['high'] - df_indicators['high'].shift(1)), 0)

            plus_di = 100 * (plus_dm.rolling(14).mean() / atr)
            minus_di = 100 * (minus_dm.rolling(14).mean() / atr)

            # ADX calculation
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(14).mean().iloc[-1]
            features.append(adx / 100 if not pd.isna(adx) else 0.0)

            # Moving average slope
            ma_20 = df_indicators['close'].rolling(20).mean()
            ma_slope = (ma_20.iloc[-1] - ma_20.iloc[-5]) / ma_20.iloc[-5] if ma_20.iloc[-5] > 0 else 0.0
            features.append(ma_slope)

            # Trend consistency (percentage of up moves in last 10 periods)
            price_changes = df_indicators['close'].diff()
            up_moves = (price_changes > 0).rolling(10).sum().iloc[-1] / 10
            features.append(up_moves)
        else:
            features.extend([0.0, 0.0, 0.5])

        return features

    def _get_support_resistance_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract support/resistance features."""
        features = []
        if len(df_indicators) >= 20:
            high_20 = df_indicators['high'].rolling(20).max().iloc[-1]
            low_20 = df_indicators['low'].rolling(20).min().iloc[-1]
            position = (latest_row['close'] - low_20) / (high_20 - low_20) if high_20 != low_20 else 0.5
            features.append(position)
        else:
            features.append(0.5)
        return features

    def _get_false_breakout_filter_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract false breakout filter features (replaces volume_breakout)."""
        features = []

        if len(df_indicators) >= 20:
            # ATR breakout threshold (3x ATR requirement)
            high_low_diff = df_indicators['high'] - df_indicators['low']
            atr = high_low_diff.rolling(14).mean().iloc[-1]
            current_price = latest_row['close']

            # Calculate recent high/low levels
            recent_high = df_indicators['high'].rolling(20).max().iloc[-1]
            recent_low = df_indicators['low'].rolling(20).min().iloc[-1]

            # ATR breakout threshold
            atr_threshold_up = (current_price - recent_high) / atr if atr > 0 else 0.0
            atr_threshold_down = (recent_low - current_price) / atr if atr > 0 else 0.0
            atr_breakout_score = max(atr_threshold_up, atr_threshold_down)
            features.append(atr_breakout_score)

            # Pre-breakout compression (Bollinger Bandwidth)
            bb_mean = df_indicators['close'].rolling(20).mean()
            bb_std = df_indicators['close'].rolling(20).std()
            bb_bandwidth = (2 * bb_std.iloc[-1]) / bb_mean.iloc[-1] if bb_mean.iloc[-1] > 0 else 0.0

            # Compare to median bandwidth (compression indicator)
            bb_bandwidth_series = (2 * bb_std) / bb_mean
            bb_median = bb_bandwidth_series.rolling(50).median().iloc[-1]
            compression_ratio = bb_bandwidth / bb_median if bb_median > 0 else 1.0
            features.append(compression_ratio)

            # Post-breakout expansion potential
            volatility_recent = df_indicators['close'].rolling(5).std().iloc[-1]
            volatility_avg = df_indicators['close'].rolling(20).std().iloc[-1]
            expansion_potential = volatility_recent / volatility_avg if volatility_avg > 0 else 1.0
            features.append(expansion_potential)
        else:
            features.extend([0.0, 1.0, 1.0])

        return features

    def _get_volatility_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility features."""
        features = []
        if 'volatility' in latest_row:
            features.append(latest_row['volatility'])
        else:
            features.append(0.0)
        return features

    def _get_volatility_regime_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility regime features."""
        features = []
        if 'regime_state' in latest_row:
            features.append(latest_row['regime_state'] / 4)  # Normalize to 0-1
        else:
            features.append(0.0)
        return features

    def _get_atr_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract ATR features."""
        features = []
        if len(df_indicators) >= 14:
            high_low = df_indicators['high'] - df_indicators['low']
            high_close = abs(df_indicators['high'] - df_indicators['close'].shift(1))
            low_close = abs(df_indicators['low'] - df_indicators['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
            features.append(atr / latest_row['close'])
        else:
            features.append(0.0)
        return features

    def _get_bollinger_width_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Bollinger Band width features."""
        features = []
        if len(df_indicators) >= 20:
            std_price = df_indicators['close'].rolling(20).std().iloc[-1]
            mean_price = df_indicators['close'].rolling(20).mean().iloc[-1]
            bb_width = (2 * std_price) / mean_price if mean_price > 0 else 0
            features.append(bb_width)
        else:
            features.append(0.0)
        return features

    def _get_synthetic_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract synthetic-specific features."""
        features = []
        synthetic_cols = ['jumpiness_score', 'volatility_compression', 'price_acceleration',
                         'tick_velocity', 'mean_reversion_signal', 'regime_state']

        for col in synthetic_cols:
            if col in latest_row:
                features.append(latest_row[col] if not pd.isna(latest_row[col]) else 0.0)
            else:
                features.append(0.0)

        return features

    def _analyze_market_regime(self, latest_data: pd.Series, df_indicators: pd.DataFrame) -> Dict:
        """Analyze current market regime and conditions."""
        regime_state = latest_data.get('regime_state', 0)
        regime_name = self.pattern_detector.regimes.get(regime_state, 'UNKNOWN')
        
        # Calculate regime-specific metrics
        jumpiness = latest_data.get('jumpiness_score', 0)
        volatility = latest_data.get('volatility', 0)
        compression = latest_data.get('volatility_compression', 0)
        acceleration = latest_data.get('price_acceleration', 0)
        
        # Detect potential jump conditions
        jump_probability = 0.0
        if regime_name == 'PRE_JUMP':
            jump_probability = min(0.8, jumpiness + compression)
        elif regime_name == 'JUMPING':
            jump_probability = 0.9
        elif jumpiness > 0.7:
            jump_probability = jumpiness * 0.6
            
        # Detect volatility expansion
        vol_expansion = False
        if len(df_indicators) >= 20:
            recent_vol = df_indicators['volatility'].iloc[-5:].mean()
            historical_vol = df_indicators['volatility'].iloc[-20:-5].mean()
            if recent_vol > historical_vol * 1.5:
                vol_expansion = True
                
        return {
            'regime_name': regime_name,
            'regime_state': regime_state,
            'jumpiness': jumpiness,
            'volatility': volatility,
            'compression': compression,
            'acceleration': acceleration,
            'jump_probability': jump_probability,
            'volatility_expansion': vol_expansion,
            'confidence_multiplier': self.regime_confidence_multiplier.get(regime_name, 1.0)
        }
        
    def _create_trading_signal(self, ensemble_prediction: Dict, regime_analysis: Dict, 
                             current_price: float, market_data: pd.DataFrame, timeframe: int) -> Optional[TradingSignal]:
        """Create a trading signal from AI prediction and market analysis."""
        try:
            # Extract AI prediction details
            ai_signal = ensemble_prediction.get('ensemble_signal', 0)
            ai_confidence = ensemble_prediction.get('confidence', 0)
            consensus = ensemble_prediction.get('consensus', 'no_models')
            consensus_strength = ensemble_prediction.get('consensus_strength', 0)
            
            # Check minimum requirements
            if ai_confidence < self.min_confidence:
                logger.debug(f"AI confidence {ai_confidence:.3f} below minimum {self.min_confidence}")
                return None
                
            if consensus_strength < self.min_consensus_strength:
                logger.debug(f"Consensus strength {consensus_strength:.3f} below minimum {self.min_consensus_strength}")
                return None
                
            # Apply regime-based confidence adjustment
            regime_multiplier = regime_analysis['confidence_multiplier']
            adjusted_confidence = min(1.0, ai_confidence * regime_multiplier)
            
            # Determine signal type based on AI prediction and regime
            signal_type = self._determine_signal_type(ai_signal, adjusted_confidence, regime_analysis)
            
            if signal_type == SignalType.NO_SIGNAL:
                return None
                
            # Calculate position sizing
            position_size = self._calculate_position_size(adjusted_confidence, regime_analysis)
            
            # Calculate stop loss and take profit (default to medium term for ensemble signals)
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                signal_type, current_price, regime_analysis, market_data, 'medium_term'
            )
            
            # Calculate risk-reward ratio
            if signal_type.value > 0:  # Buy signal
                risk = current_price - stop_loss
                reward = take_profit - current_price
            else:  # Sell signal
                risk = stop_loss - current_price
                reward = current_price - take_profit
                
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # Check minimum risk-reward ratio
            if risk_reward_ratio < self.min_risk_reward:
                logger.debug(f"Risk-reward ratio {risk_reward_ratio:.2f} below minimum {self.min_risk_reward}")
                return None

            # Create reasoning
            reasoning = self._generate_reasoning(ai_signal, regime_analysis, ensemble_prediction)

            # Create trading signal
            signal = TradingSignal(
                signal_type=signal_type,
                confidence=adjusted_confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                risk_reward_ratio=risk_reward_ratio,
                timeframe=timeframe,
                timestamp=datetime.now(),
                ai_predictions=ensemble_prediction,
                market_regime=regime_analysis['regime_name'],
                reasoning=reasoning
            )

            return signal

        except Exception as e:
            logger.error(f"Error creating trading signal: {e}")
            return None

    def _determine_signal_type(self, ai_signal: int, confidence: float, regime_analysis: Dict) -> SignalType:
        """Determine the trading signal type based on AI prediction and market regime."""
        # No signal for very low confidence
        if confidence < 0.4:
            return SignalType.NO_SIGNAL

        # Adjust signal strength based on regime
        regime_name = regime_analysis['regime_name']
        jump_probability = regime_analysis['jump_probability']

        # Strong signals during high-probability jump conditions
        if jump_probability > 0.7:
            if ai_signal >= 1:
                return SignalType.STRONG_BUY
            elif ai_signal <= -1:
                return SignalType.STRONG_SELL

        # Normal signal determination
        if ai_signal == 2:
            return SignalType.STRONG_BUY if confidence > 0.7 else SignalType.WEAK_BUY
        elif ai_signal == 1:
            return SignalType.WEAK_BUY
        elif ai_signal == -1:
            return SignalType.WEAK_SELL
        elif ai_signal == -2:
            return SignalType.STRONG_SELL if confidence > 0.7 else SignalType.WEAK_SELL
        else:
            return SignalType.NO_SIGNAL

    def _calculate_position_size(self, confidence: float, regime_analysis: Dict) -> float:
        """Calculate position size based on confidence and market conditions."""
        # Base position size from confidence
        base_size = self.base_risk_per_trade * confidence

        # Adjust for regime
        regime_name = regime_analysis['regime_name']
        if regime_name == 'JUMPING':
            base_size *= 1.5  # Increase size during jumps
        elif regime_name == 'QUIET':
            base_size *= 0.7  # Reduce size during quiet periods

        # Adjust for volatility
        volatility = regime_analysis['volatility']
        if volatility > 0.02:  # High volatility
            base_size *= 0.8
        elif volatility < 0.005:  # Low volatility
            base_size *= 1.2

        # Cap at maximum position size
        return min(base_size, self.max_position_size)

    def _map_timeframe_category_to_group(self, timeframe_category: str) -> str:
        """Map timeframe category to SL/TP group."""
        if not timeframe_category:
            return 'medium_term'
        
        category_lower = timeframe_category.lower()
        
        # Map timeframe categories to groups
        if any(term in category_lower for term in ['short', 'scalp', 'quick', 'fast', '1m', '5m']):
            return 'short_term'
        elif any(term in category_lower for term in ['long', 'trend', 'position', 'daily', '1d', '4h']):
            return 'long_term'
        else:
            # Default to medium term for swing, 15m, 30m, 1h, etc.
            return 'medium_term'

    def _calculate_stop_loss_take_profit(self, signal_type: SignalType, current_price: float,
                                       regime_analysis: Dict, market_data: pd.DataFrame,
                                       timeframe_group: Optional[str] = None) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels with fixed timeframe-specific distances."""

        # FIXED TIMEFRAME-SPECIFIC SL/TP DISTANCES (in points)
        # Updated per user request: Fixed values without dynamic adjustments

        # Check if this is a SELL trade
        is_sell_trade = signal_type.value < 0

        if is_sell_trade:
            # FIXED SL/TP FOR SELL TRADES
            timeframe_distances = {
                'short_term': {
                    'sl_distance': 50.0,      # 50 points SL for sell trades
                    'tp_distance': 100.0,     # 100 points TP for sell trades
                },
                'medium_term': {
                    'sl_distance': 150.0,     # 150 points SL for sell trades
                    'tp_distance': 100.0,     # 100 points TP for sell trades
                },
                'long_term': {
                    'sl_distance': 300.0,     # 300 points SL for sell trades
                    'tp_distance': 100.0,     # 100 points TP for sell trades
                }
            }
        else:
            # FIXED SL/TP FOR BUY TRADES
            timeframe_distances = {
                'short_term': {
                    'sl_distance': 50.0,      # 50 points SL for quick scalping
                    'tp_distance': 100.0,     # 100 points TP for quick profit
                },
                'medium_term': {
                    'sl_distance': 125.0,     # 125 points SL for swing trading
                    'tp_distance': 250.0,     # 250 points TP for medium targets
                },
                'long_term': {
                    'sl_distance': 250.0,     # 250 points SL for trend following
                    'tp_distance': 500.0,     # 500 points TP for larger targets
                }
            }

        # Default to medium term if timeframe_group not specified
        if timeframe_group not in timeframe_distances:
            timeframe_group = 'medium_term'

        distances = timeframe_distances[timeframe_group]
        sl_distance = distances['sl_distance']
        tp_distance = distances['tp_distance']

        logger.debug(f"Using {timeframe_group} FIXED SL/TP: SL={sl_distance}pts, TP={tp_distance}pts")

        # NO DYNAMIC ADJUSTMENTS - Using fixed values only
        # Removed volatility and regime-based adjustments per user request

        # Calculate stop loss and take profit based on signal direction
        if signal_type.value > 0:  # Buy signal
            stop_loss = current_price - sl_distance
            take_profit = current_price + tp_distance
        else:  # Sell signal
            stop_loss = current_price + sl_distance
            take_profit = current_price - tp_distance

        logger.debug(f"Final FIXED SL/TP for {timeframe_group}: SL={stop_loss:.2f} ({sl_distance:.1f}pts), TP={take_profit:.2f} ({tp_distance:.1f}pts)")

        return stop_loss, take_profit



    def _generate_reasoning(self, ai_signal: int, regime_analysis: Dict, ensemble_prediction: Dict) -> str:
        """Generate human-readable reasoning for the trading signal."""
        reasoning_parts = []

        # AI prediction reasoning
        confidence = ensemble_prediction.get('confidence', 0)
        consensus = ensemble_prediction.get('consensus', 'unknown')
        total_models = ensemble_prediction.get('total_models', 0)

        reasoning_parts.append(f"AI Ensemble: {ai_signal} signal with {confidence:.1%} confidence")
        reasoning_parts.append(f"Model consensus: {consensus} ({total_models} models)")

        # Regime reasoning
        regime_name = regime_analysis['regime_name']
        jump_prob = regime_analysis['jump_probability']
        reasoning_parts.append(f"Market regime: {regime_name}")

        if jump_prob > 0.5:
            reasoning_parts.append(f"Jump probability: {jump_prob:.1%}")

        # Volatility reasoning
        if regime_analysis['volatility_expansion']:
            reasoning_parts.append("Volatility expansion detected")

        # Jumpiness reasoning
        jumpiness = regime_analysis['jumpiness']
        if jumpiness > 0.7:
            reasoning_parts.append(f"High jumpiness: {jumpiness:.1%}")

        return " | ".join(reasoning_parts)

    def _can_generate_signal(self) -> bool:
        """Check if we can generate a new signal based on risk limits."""
        # Check if trading is allowed (daily limits reached)
        if hasattr(self, 'order_executor') and self.order_executor:
            if not self.order_executor.is_trading_allowed():
                logger.debug("Signal generation blocked - daily limits reached or outside schedule")
                return False

        # Check recent signal frequency (prevent over-trading)
        now = datetime.now()
        recent_signals = [s for s in self.recent_signals if (now - s.timestamp).seconds < 300]  # 5 minutes
        if len(recent_signals) >= 2:
            logger.debug("Too many recent signals, waiting...")
            return False

        # CRITICAL: Prevent rapid signal generation (minimum 30 seconds between signals)
        if self.recent_signals:
            last_signal_time = self.recent_signals[-1].timestamp
            time_since_last = (now - last_signal_time).total_seconds()
            if time_since_last < 30:  # 30 second minimum between signals
                logger.debug(f"Signal too soon after last signal: {time_since_last:.1f}s < 30s")
                return False

        return True

    def _save_predictions_to_shared_cache(self, model_features: Dict[str, np.ndarray], ensemble_prediction: Dict):
        """Save current model predictions to shared cache for dashboard synchronization."""
        try:
            # Create data directory if it doesn't exist
            os.makedirs("data", exist_ok=True)

            # Generate individual predictions for each model
            predictions_cache = {}

            for model_name, features in model_features.items():
                try:
                    prediction = self.ai_manager.predict(model_name, features)
                    if prediction:
                        predictions_cache[model_name] = {
                            "signal": prediction["signal"],
                            "confidence": prediction["confidence"],
                            "model_type": prediction["model_type"],
                            "timestamp": datetime.now().strftime('%H:%M:%S')
                        }
                except Exception as e:
                    logger.warning(f"Error getting prediction for {model_name}: {e}")

            # Create cache data structure
            cache_data = {
                "timestamp": datetime.now().isoformat(),
                "predictions": predictions_cache,
                "ensemble_data": {
                    "ensemble_signal": ensemble_prediction.get("ensemble_signal", 0),
                    "confidence": ensemble_prediction.get("confidence", 0.0),
                    "consensus": ensemble_prediction.get("consensus", "unknown"),
                    "strong_signals": ensemble_prediction.get("strong_signals", []),
                    "timeframe_consensus_signals": ensemble_prediction.get("timeframe_consensus_signals", [])
                }
            }

            # Save to shared cache file
            cache_file = "data/shared_predictions_cache.json"
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.debug(f"Saved {len(predictions_cache)} predictions to shared cache")

        except Exception as e:
            logger.warning(f"Error saving predictions to shared cache: {e}")

    def _track_signal(self, signal: TradingSignal):
        """Track generated signals for analysis and limits."""
        self.recent_signals.append(signal)
        self.daily_trade_count += 1

        # Keep only recent signals (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.recent_signals = [s for s in self.recent_signals if s.timestamp > cutoff_time]

    def _reset_daily_counters(self):
        """Reset daily counters if new day."""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_trade_count = 0
            self.last_reset_date = current_date
            logger.info("Daily trade counters reset")

    def get_signal_statistics(self) -> Dict:
        """Get statistics about recent signals."""
        if not self.recent_signals:
            return {"total_signals": 0}

        recent_24h = [s for s in self.recent_signals if (datetime.now() - s.timestamp).hours < 24]

        signal_types = {}
        confidences = []
        risk_rewards = []

        for signal in recent_24h:
            signal_type = signal.signal_type.name
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
            confidences.append(signal.confidence)
            risk_rewards.append(signal.risk_reward_ratio)

        return {
            "total_signals": len(recent_24h),
            "daily_count": self.daily_trade_count,
            "signal_types": signal_types,
            "avg_confidence": np.mean(confidences) if confidences else 0,
            "avg_risk_reward": np.mean(risk_rewards) if risk_rewards else 0,
            "max_confidence": max(confidences) if confidences else 0,
            "min_confidence": min(confidences) if confidences else 0
        }

    def _get_short_term_trend_direction_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract short-term trend direction features"""
        features = []
        
        if len(df_indicators) >= 30:
            # 5-minute trend direction
            ma_5 = df_indicators['close'].rolling(5).mean()
            ma_10 = df_indicators['close'].rolling(10).mean()
            trend_5m = (ma_5.iloc[-1] - ma_10.iloc[-1]) / latest_row['close']
            features.append(trend_5m if not np.isnan(trend_5m) else 0.0)
            
            # 15-minute trend direction
            ma_15 = df_indicators['close'].rolling(15).mean()
            ma_30 = df_indicators['close'].rolling(30).mean()
            trend_15m = (ma_15.iloc[-1] - ma_30.iloc[-1]) / latest_row['close']
            features.append(trend_15m if not np.isnan(trend_15m) else 0.0)
            
            # Trend strength
            price_change = (latest_row['close'] - df_indicators['close'].iloc[-21]) / df_indicators['close'].iloc[-21]
            features.append(price_change if not np.isnan(price_change) else 0.0)
            
            # Trend consistency
            returns = df_indicators['close'].pct_change().iloc[-10:]
            consistency = (returns > 0).sum() / len(returns) if len(returns) > 0 else 0.5
            features.append(consistency)
        else:
            features.extend([0.0, 0.0, 0.0, 0.5])
            
        return features

    def _get_medium_term_trend_direction_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract medium-term trend direction features"""
        features = []
        
        if len(df_indicators) >= 120:
            # 1-hour trend direction
            ma_60 = df_indicators['close'].rolling(60).mean()
            ma_120 = df_indicators['close'].rolling(120).mean()
            trend_1h = (ma_60.iloc[-1] - ma_120.iloc[-1]) / latest_row['close']
            features.append(trend_1h if not np.isnan(trend_1h) else 0.0)
            
            # 4-hour trend direction
            ma_240 = df_indicators['close'].rolling(240).mean() if len(df_indicators) >= 240 else ma_120
            ma_480 = df_indicators['close'].rolling(480).mean() if len(df_indicators) >= 480 else ma_240
            trend_4h = (ma_240.iloc[-1] - ma_480.iloc[-1]) / latest_row['close']
            features.append(trend_4h if not np.isnan(trend_4h) else 0.0)
            
            # Medium-term momentum
            momentum = (latest_row['close'] - df_indicators['close'].iloc[-101]) / df_indicators['close'].iloc[-101] if len(df_indicators) >= 101 else 0.0
            features.append(momentum if not np.isnan(momentum) else 0.0)
            
            # Trend acceleration
            if len(df_indicators) >= 50:
                recent_trend = (latest_row['close'] - df_indicators['close'].iloc[-26]) / df_indicators['close'].iloc[-26]
                older_trend = (df_indicators['close'].iloc[-26] - df_indicators['close'].iloc[-51]) / df_indicators['close'].iloc[-51]
                acceleration = recent_trend - older_trend
                features.append(acceleration if not np.isnan(acceleration) else 0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
            
        return features

    def _get_long_term_trend_direction_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract long-term trend direction features"""
        features = []
        
        if len(df_indicators) >= 1440:
            # Daily trend direction
            ma_1440 = df_indicators['close'].rolling(1440).mean()
            ma_2880 = df_indicators['close'].rolling(2880).mean() if len(df_indicators) >= 2880 else ma_1440
            trend_daily = (ma_1440.iloc[-1] - ma_2880.iloc[-1]) / latest_row['close']
            features.append(trend_daily if not np.isnan(trend_daily) else 0.0)
            
            # Weekly trend direction
            ma_weekly = df_indicators['close'].rolling(min(len(df_indicators), 10080)).mean()
            ma_biweekly = df_indicators['close'].rolling(min(len(df_indicators), 20160)).mean()
            trend_weekly = (ma_weekly.iloc[-1] - ma_biweekly.iloc[-1]) / latest_row['close']
            features.append(trend_weekly if not np.isnan(trend_weekly) else 0.0)
            
            # Long-term momentum
            long_momentum = (latest_row['close'] - df_indicators['close'].iloc[-1441]) / df_indicators['close'].iloc[-1441]
            features.append(long_momentum if not np.isnan(long_momentum) else 0.0)
            
            # Macro trend strength
            if len(df_indicators) >= 2880:
                macro_strength = abs((latest_row['close'] - df_indicators['close'].iloc[-2881]) / df_indicators['close'].iloc[-2881])
                features.append(macro_strength if not np.isnan(macro_strength) else 0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
            
        return features

    def _get_trend_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract trend alignment features"""
        features = []
        
        if len(df_indicators) >= 50:
            # Multi-timeframe trend consensus
            ma_5 = df_indicators['close'].rolling(5).mean().iloc[-1]
            ma_20 = df_indicators['close'].rolling(20).mean().iloc[-1]
            ma_50 = df_indicators['close'].rolling(50).mean().iloc[-1]
            
            trend_5_20 = 1 if ma_5 > ma_20 else -1
            trend_20_50 = 1 if ma_20 > ma_50 else -1
            consensus = (trend_5_20 + trend_20_50) / 2
            features.append(consensus)
            
            # Timeframe divergence
            short_trend = (ma_5 - ma_20) / latest_row['close']
            long_trend = (ma_20 - ma_50) / latest_row['close']
            divergence = abs(short_trend - long_trend)
            features.append(divergence if not np.isnan(divergence) else 0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_momentum_trend_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract momentum trend alignment features"""
        features = []
        
        if len(df_indicators) >= 30:
            # Momentum vs trend direction
            returns = df_indicators['close'].pct_change().iloc[-10:]
            momentum_direction = 1 if returns.mean() > 0 else -1
            
            ma_20 = df_indicators['close'].rolling(20).mean()
            trend_direction = 1 if ma_20.iloc[-1] > ma_20.iloc[-11] else -1
            
            alignment = momentum_direction * trend_direction
            features.append(alignment)
            
            # Momentum strength in trend context
            momentum_strength = abs(returns.mean()) / (returns.std() + 1e-8)
            features.append(momentum_strength if not np.isnan(momentum_strength) else 0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_reversion_trend_context_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract reversion trend context features"""
        features = []
        
        if len(df_indicators) >= 30:
            # Reversion probability in trend
            ma_20 = df_indicators['close'].rolling(20).mean()
            deviation = (latest_row['close'] - ma_20.iloc[-1]) / ma_20.iloc[-1]
            reversion_prob = min(abs(deviation) * 10, 1.0)  # Cap at 1.0
            features.append(reversion_prob)
            
            # Trend strength vs reversion signal
            trend_strength = abs((ma_20.iloc[-1] - ma_20.iloc[-11]) / ma_20.iloc[-11])
            reversion_strength = abs(deviation)
            ratio = reversion_strength / (trend_strength + 1e-8)
            features.append(ratio if not np.isnan(ratio) else 0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_cross_timeframe_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract cross-timeframe alignment features"""
        features = []
        
        if len(df_indicators) >= 100:
            # Multi-timeframe trend consensus
            ma_10 = df_indicators['close'].rolling(10).mean().iloc[-1]
            ma_50 = df_indicators['close'].rolling(50).mean().iloc[-1]
            ma_100 = df_indicators['close'].rolling(100).mean().iloc[-1]
            
            trends = []
            if ma_10 > ma_50: trends.append(1)
            else: trends.append(-1)
            if ma_50 > ma_100: trends.append(1)
            else: trends.append(-1)
            
            consensus = sum(trends) / len(trends)
            features.append(consensus)
            
            # Timeframe divergence
            short_trend = (ma_10 - ma_50) / latest_row['close']
            long_trend = (ma_50 - ma_100) / latest_row['close']
            divergence = abs(short_trend - long_trend)
            features.append(divergence if not np.isnan(divergence) else 0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_breakout_trend_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract breakout trend alignment features"""
        features = []
        
        if len(df_indicators) >= 50:
            # Breakout direction vs trend direction
            high_20 = df_indicators['high'].rolling(20).max().iloc[-1]
            low_20 = df_indicators['low'].rolling(20).min().iloc[-1]
            
            breakout_direction = 0
            if latest_row['close'] > high_20:
                breakout_direction = 1
            elif latest_row['close'] < low_20:
                breakout_direction = -1
                
            ma_50 = df_indicators['close'].rolling(50).mean()
            trend_direction = 1 if ma_50.iloc[-1] > ma_50.iloc[-26] else -1
            
            alignment = breakout_direction * trend_direction
            features.append(alignment)
            
            # Breakout strength in trend context
            if breakout_direction != 0:
                if breakout_direction == 1:
                    strength = (latest_row['close'] - high_20) / high_20
                else:
                    strength = (low_20 - latest_row['close']) / low_20
                features.append(strength if not np.isnan(strength) else 0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_volatility_trend_context_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility trend context features"""
        features = []
        
        if len(df_indicators) >= 30:
            # Volatility regime vs trend strength
            returns = df_indicators['close'].pct_change().iloc[-20:]
            volatility = returns.std()
            
            ma_20 = df_indicators['close'].rolling(20).mean()
            trend_strength = abs((ma_20.iloc[-1] - ma_20.iloc[-11]) / ma_20.iloc[-11])
            
            vol_trend_ratio = volatility / (trend_strength + 1e-8)
            features.append(vol_trend_ratio if not np.isnan(vol_trend_ratio) else 0.0)
            
            # Volatility compression in trend
            vol_ma = returns.rolling(10).std().iloc[-10:]
            vol_compression = vol_ma.iloc[-1] / (vol_ma.mean() + 1e-8)
            features.append(vol_compression if not np.isnan(vol_compression) else 1.0)
        else:
            features.extend([0.0, 1.0])
            
        return features

    def _get_macro_trend_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract macro trend alignment features"""
        features = []
        
        if len(df_indicators) >= 200:
            # Long-term trend vs short-term signals
            ma_200 = df_indicators['close'].rolling(200).mean().iloc[-1]
            ma_20 = df_indicators['close'].rolling(20).mean().iloc[-1]
            
            long_trend = 1 if latest_row['close'] > ma_200 else -1
            short_signal = 1 if ma_20 > ma_200 else -1
            
            alignment = long_trend * short_signal
            features.append(alignment)
            
            # Macro trend strength
            macro_strength = abs((latest_row['close'] - ma_200) / ma_200)
            features.append(macro_strength if not np.isnan(macro_strength) else 0.0)
        else:
            features.extend([0.0, 0.0])
            
        return features

    def _get_level_trend_context_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract level trend context features"""
        features = []
        
        if len(df_indicators) >= 100:
            # Find recent support/resistance levels
            lookback = min(100, len(df_indicators))
            highs = df_indicators['high'].iloc[-lookback:]
            lows = df_indicators['low'].iloc[-lookback:]
            
            resistance = highs.max()
            support = lows.min()
            current_price = latest_row['close']
            
            # Distance to key levels
            dist_to_resistance = (resistance - current_price) / current_price
            dist_to_support = (current_price - support) / current_price
            
            features.extend([dist_to_resistance, dist_to_support])
            
            # Level sustainability in trend
            if len(df_indicators) >= 100:
                trend_direction = (df_indicators['close'].rolling(50).mean().iloc[-1] - df_indicators['close'].rolling(100).mean().iloc[-1]) / df_indicators['close'].iloc[-1]
                
                # In uptrend, support more reliable; in downtrend, resistance more reliable
                level_sustainability = trend_direction * (dist_to_support - dist_to_resistance)
                features.append(level_sustainability if not np.isnan(level_sustainability) else 0.0)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.0, 0.0])
            
        return features

    def _get_portfolio_trend_alignment_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract portfolio trend alignment features"""
        features = []
        
        if len(df_indicators) >= 100:
            # Risk-adjusted trend strength
            returns = df_indicators['close'].pct_change().iloc[-50:]
            trend_return = returns.mean()
            trend_volatility = returns.std()
            
            risk_adjusted_strength = trend_return / (trend_volatility + 1e-8)
            features.append(risk_adjusted_strength if not np.isnan(risk_adjusted_strength) else 0.0)
            
            # Allocation confidence based on trend consistency
            ma_20 = df_indicators['close'].rolling(20).mean()
            ma_50 = df_indicators['close'].rolling(50).mean()
            ma_100 = df_indicators['close'].rolling(100).mean()
            
            trend_consistency = 0
            if ma_20.iloc[-1] > ma_50.iloc[-1] > ma_100.iloc[-1]:
                trend_consistency = 1  # Strong uptrend
            elif ma_20.iloc[-1] < ma_50.iloc[-1] < ma_100.iloc[-1]:
                trend_consistency = -1  # Strong downtrend
            
            confidence = abs(trend_consistency)
            features.append(confidence)
        else:
            features.extend([0.0, 0.0])
            
        return features
