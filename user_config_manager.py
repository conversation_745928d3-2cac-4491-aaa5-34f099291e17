#!/usr/bin/env python3
"""
User Configuration Manager for AI Trading System
Handles manual lot size input and user preferences at system startup.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# Set up logging
logger = logging.getLogger('UserConfigManager')

class UserConfigManager:
    """Manages user configuration including manual lot size input."""
    
    def __init__(self):
        """Initialize the user configuration manager."""
        self.config_file = "user_config.json"
        self.backup_config_file = "user_config_backup.json"
        self.default_config = {
            "lot_size": {
                "mode": "auto",  # "auto" or "manual"
                "manual_lot_size": 0.01,  # Default manual lot size
                "last_updated": None
            },
            "risk_settings": {
                "max_risk_per_trade": 0.005,  # 0.5%
                "max_position_size": 0.01,    # 1%
                "use_custom_risk": False
            },
            "trading_preferences": {
                "confirm_trades": False,
                "enable_notifications": True,
                "auto_start_trading": True
            },
            "system_info": {
                "created": None,
                "last_modified": None,
                "version": "2.1"
            }
        }
        
    def prompt_user_configuration(self) -> bool:
        """Prompt user for configuration at system startup."""
        try:
            print("\n" + "="*60)
            print("🔧 AI TRADING SYSTEM - USER CONFIGURATION")
            print("="*60)
            
            # Load existing config if available
            existing_config = self.load_user_config()
            
            if existing_config:
                print(f"📋 Found existing configuration:")
                self._display_current_config(existing_config)
                
                # Ask if user wants to modify
                modify = input("\n❓ Do you want to modify your configuration? (y/N): ").strip().lower()
                if modify not in ['y', 'yes']:
                    print("✅ Using existing configuration")
                    return True
            
            # Get lot size configuration
            lot_config = self._configure_lot_size(existing_config)
            
            # Get risk settings (optional)
            risk_config = self._configure_risk_settings(existing_config)
            
            # Get trading preferences (optional)
            trading_config = self._configure_trading_preferences(existing_config)
            
            # Build final configuration
            final_config = self._build_final_config(lot_config, risk_config, trading_config)
            
            # Save configuration
            if self.save_user_config(final_config):
                print("\n✅ Configuration saved successfully!")
                self._display_final_config(final_config)
                return True
            else:
                print("\n❌ Failed to save configuration")
                return False
                
        except KeyboardInterrupt:
            print("\n\n⚠️ Configuration cancelled by user")
            return False
        except Exception as e:
            logger.error(f"Error in user configuration: {e}")
            print(f"\n❌ Configuration error: {e}")
            return False
    
    def _configure_lot_size(self, existing_config: Optional[Dict]) -> Dict:
        """Configure lot size settings."""
        print("\n📊 LOT SIZE CONFIGURATION")
        print("-" * 30)
        
        current_mode = "auto"
        current_lot_size = 0.01
        
        if existing_config and "lot_size" in existing_config:
            current_mode = existing_config["lot_size"].get("mode", "auto")
            current_lot_size = existing_config["lot_size"].get("manual_lot_size", 0.01)
        
        print(f"Current mode: {current_mode}")
        if current_mode == "manual":
            print(f"Current manual lot size: {current_lot_size}")
        
        print("\nLot size options:")
        print("1. Auto (system calculates based on account balance and risk)")
        print("2. Manual (you specify exact lot size)")
        
        while True:
            choice = input("\nSelect option (1 or 2): ").strip()
            
            if choice == "1":
                return {
                    "mode": "auto",
                    "manual_lot_size": current_lot_size,
                    "last_updated": datetime.now().isoformat()
                }
            elif choice == "2":
                # Get manual lot size
                while True:
                    try:
                        lot_input = input(f"\nEnter manual lot size (current: {current_lot_size}): ").strip()
                        if not lot_input:  # Use current value
                            manual_lot = current_lot_size
                        else:
                            manual_lot = float(lot_input)
                        
                        if manual_lot <= 0:
                            print("❌ Lot size must be greater than 0")
                            continue
                        if manual_lot > 10:
                            print("❌ Lot size seems too large (max recommended: 10)")
                            continue
                            
                        print(f"✅ Manual lot size set to: {manual_lot}")
                        return {
                            "mode": "manual",
                            "manual_lot_size": manual_lot,
                            "last_updated": datetime.now().isoformat()
                        }
                        
                    except ValueError:
                        print("❌ Invalid number. Please enter a valid lot size.")
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
    
    def _configure_risk_settings(self, existing_config: Optional[Dict]) -> Dict:
        """Configure risk management settings (optional)."""
        print("\n⚠️ RISK MANAGEMENT SETTINGS (Optional)")
        print("-" * 40)
        
        current_risk = existing_config.get("risk_settings", {}) if existing_config else {}
        
        modify_risk = input("Do you want to customize risk settings? (y/N): ").strip().lower()
        if modify_risk not in ['y', 'yes']:
            return current_risk or self.default_config["risk_settings"]
        
        # Configure custom risk settings
        risk_config = current_risk.copy() if current_risk else self.default_config["risk_settings"].copy()
        
        print(f"\nCurrent max risk per trade: {risk_config.get('max_risk_per_trade', 0.005)*100}%")
        risk_input = input("Enter new max risk per trade % (or press Enter to keep current): ").strip()
        if risk_input:
            try:
                risk_pct = float(risk_input) / 100
                if 0 < risk_pct <= 0.1:  # Max 10%
                    risk_config['max_risk_per_trade'] = risk_pct
                    risk_config['use_custom_risk'] = True
                else:
                    print("❌ Risk percentage must be between 0.1% and 10%")
            except ValueError:
                print("❌ Invalid percentage")
        
        return risk_config
    
    def _configure_trading_preferences(self, existing_config: Optional[Dict]) -> Dict:
        """Configure trading preferences (optional)."""
        print("\n🎯 TRADING PREFERENCES (Optional)")
        print("-" * 35)
        
        current_prefs = existing_config.get("trading_preferences", {}) if existing_config else {}
        
        modify_prefs = input("Do you want to customize trading preferences? (y/N): ").strip().lower()
        if modify_prefs not in ['y', 'yes']:
            return current_prefs or self.default_config["trading_preferences"]
        
        prefs = current_prefs.copy() if current_prefs else self.default_config["trading_preferences"].copy()
        
        # Auto-start trading
        auto_start = input(f"Auto-start trading after system startup? (Y/n): ").strip().lower()
        prefs['auto_start_trading'] = auto_start not in ['n', 'no']
        
        return prefs
    
    def _build_final_config(self, lot_config: Dict, risk_config: Dict, trading_config: Dict) -> Dict:
        """Build the final configuration dictionary."""
        config = self.default_config.copy()
        config["lot_size"] = lot_config
        config["risk_settings"] = risk_config
        config["trading_preferences"] = trading_config
        config["system_info"]["last_modified"] = datetime.now().isoformat()
        
        if not config["system_info"]["created"]:
            config["system_info"]["created"] = datetime.now().isoformat()
        
        return config
    
    def _display_current_config(self, config: Dict):
        """Display current configuration."""
        lot_config = config.get("lot_size", {})
        print(f"   Lot size mode: {lot_config.get('mode', 'auto')}")
        if lot_config.get('mode') == 'manual':
            print(f"   Manual lot size: {lot_config.get('manual_lot_size', 0.01)}")
        
        risk_config = config.get("risk_settings", {})
        if risk_config.get('use_custom_risk'):
            print(f"   Custom risk per trade: {risk_config.get('max_risk_per_trade', 0.005)*100}%")
    
    def _display_final_config(self, config: Dict):
        """Display final configuration summary."""
        print("\n📋 FINAL CONFIGURATION SUMMARY:")
        print("-" * 35)
        
        lot_config = config["lot_size"]
        print(f"🎯 Lot Size Mode: {lot_config['mode'].upper()}")
        if lot_config['mode'] == 'manual':
            print(f"📊 Manual Lot Size: {lot_config['manual_lot_size']}")
        
        risk_config = config["risk_settings"]
        if risk_config.get('use_custom_risk'):
            print(f"⚠️ Custom Risk per Trade: {risk_config['max_risk_per_trade']*100}%")
        
        trading_config = config["trading_preferences"]
        print(f"🚀 Auto-start Trading: {'Yes' if trading_config['auto_start_trading'] else 'No'}")
    
    def load_user_config(self) -> Optional[Dict]:
        """Load user configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info("User configuration loaded successfully")
                return config
            else:
                logger.info("No existing user configuration found")
                return None
        except Exception as e:
            logger.error(f"Error loading user configuration: {e}")
            # Try backup file
            try:
                if os.path.exists(self.backup_config_file):
                    with open(self.backup_config_file, 'r') as f:
                        config = json.load(f)
                    logger.info("User configuration loaded from backup")
                    return config
            except Exception as backup_e:
                logger.error(f"Error loading backup configuration: {backup_e}")
            return None
    
    def save_user_config(self, config: Dict) -> bool:
        """Save user configuration to file with backup."""
        try:
            # Create backup of existing config
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    backup_config = json.load(f)
                with open(self.backup_config_file, 'w') as f:
                    json.dump(backup_config, f, indent=2)
            
            # Save new config
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info("User configuration saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving user configuration: {e}")
            return False
    
    def get_lot_size_config(self) -> Dict:
        """Get current lot size configuration."""
        config = self.load_user_config()
        if config and "lot_size" in config:
            return config["lot_size"]
        return self.default_config["lot_size"]
    
    def get_manual_lot_size(self) -> Optional[float]:
        """Get manual lot size if configured."""
        lot_config = self.get_lot_size_config()
        if lot_config.get("mode") == "manual":
            return lot_config.get("manual_lot_size")
        return None

def main():
    """Test the user configuration manager."""
    manager = UserConfigManager()
    success = manager.prompt_user_configuration()
    if success:
        print("\n✅ Configuration completed successfully!")
    else:
        print("\n❌ Configuration failed!")

if __name__ == "__main__":
    main()
