#!/usr/bin/env python3
"""
User Configuration Manager for AI Trading System
Handles manual lot size input and user preferences at system startup.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# Set up logging
logger = logging.getLogger('UserConfigManager')

class UserConfigManager:
    """Manages user configuration including manual lot size input."""
    
    def __init__(self):
        """Initialize the user configuration manager."""
        self.config_file = "user_config.json"
        self.backup_config_file = "user_config_backup.json"
        self.default_config = {
            "lot_size": {
                "mode": "auto",  # "auto" or "manual"
                "manual_lot_size": 0.01,  # Default manual lot size
                "last_updated": None
            },
            "risk_settings": {
                "max_risk_per_trade": 0.005,  # 0.5%
                "max_position_size": 0.01,    # 1%
                "use_custom_risk": False
            },
            "trading_preferences": {
                "confirm_trades": False,
                "enable_notifications": True,
                "auto_start_trading": True
            },
            "system_info": {
                "created": None,
                "last_modified": None,
                "version": "2.1"
            }
        }
        
    def prompt_user_configuration(self) -> bool:
        """Prompt user for lot size configuration only - everything else is auto-calculated."""
        try:
            print("\n" + "="*60)
            print("🔧 AI TRADING SYSTEM - LOT SIZE CONFIGURATION")
            print("="*60)

            # Load existing config if available
            existing_config = self.load_user_config()

            if existing_config:
                print(f"📋 Found existing configuration:")
                self._display_current_config(existing_config)

                # Ask if user wants to modify
                modify = input("\n❓ Do you want to modify your lot size? (y/N): ").strip().lower()
                if modify not in ['y', 'yes']:
                    print("✅ Using existing configuration")
                    return True

            # Get lot size configuration ONLY
            lot_config = self._configure_lot_size_only(existing_config)

            # Auto-calculate all other settings based on lot size
            calculated_config = self._auto_calculate_settings(lot_config)

            # Save configuration
            if self.save_user_config(calculated_config):
                print("\n✅ Configuration saved successfully!")

                # Update system config files
                self.update_system_config(calculated_config)

                self._display_calculated_config(calculated_config)
                return True
            else:
                print("\n❌ Failed to save configuration")
                return False

        except KeyboardInterrupt:
            print("\n\n⚠️ Configuration cancelled by user")
            return False
        except Exception as e:
            logger.error(f"Error in user configuration: {e}")
            print(f"\n❌ Configuration error: {e}")
            return False
    
    def _configure_lot_size_only(self, existing_config: Optional[Dict]) -> Dict:
        """Configure lot size settings only - simplified interface."""
        print("\n📊 LOT SIZE CONFIGURATION")
        print("-" * 30)

        current_mode = "auto"
        current_lot_size = 0.01

        if existing_config and "lot_size" in existing_config:
            current_mode = existing_config["lot_size"].get("mode", "auto")
            current_lot_size = existing_config["lot_size"].get("manual_lot_size", 0.01)

        print(f"Current mode: {current_mode}")
        if current_mode == "manual":
            print(f"Current manual lot size: {current_lot_size}")

        print("\nLot size options:")
        print("1. Auto (system calculates based on account balance and risk)")
        print("2. Manual (you specify exact lot size)")
        print("\n💡 Note: All other settings (profit/loss limits, risk management) will be")
        print("   automatically calculated based on your lot size choice.")

        while True:
            choice = input("\nSelect option (1 or 2): ").strip()

            if choice == "1":
                print("✅ Auto lot size mode selected")
                return {
                    "mode": "auto",
                    "manual_lot_size": current_lot_size,
                    "last_updated": datetime.now().isoformat()
                }
            elif choice == "2":
                # Get manual lot size
                while True:
                    try:
                        lot_input = input(f"\nEnter manual lot size (current: {current_lot_size}): ").strip()
                        if not lot_input:  # Use current value
                            manual_lot = current_lot_size
                        else:
                            manual_lot = float(lot_input)

                        if manual_lot <= 0:
                            print("❌ Lot size must be greater than 0")
                            continue
                        if manual_lot > 10:
                            print("❌ Lot size seems too large (max recommended: 10)")
                            continue

                        print(f"✅ Manual lot size set to: {manual_lot}")
                        return {
                            "mode": "manual",
                            "manual_lot_size": manual_lot,
                            "last_updated": datetime.now().isoformat()
                        }

                    except ValueError:
                        print("❌ Invalid number. Please enter a valid lot size.")
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")

    def _auto_calculate_settings(self, lot_config: Dict) -> Dict:
        """Auto-calculate all settings based on lot size configuration."""
        print("\n🔄 AUTO-CALCULATING SETTINGS BASED ON LOT SIZE...")
        print("-" * 50)

        # Get lot size for calculations
        lot_size = lot_config.get("manual_lot_size", 0.01)
        is_manual = lot_config.get("mode") == "manual"

        # Calculate settings based on lot size
        if is_manual:
            print(f"📊 Calculating settings for manual lot size: {lot_size}")

            # Base values for 0.01 lot size
            base_lot_size = 0.01
            base_profit_limit = 20.0
            base_loss_limit = 10.0

            # Calculate scaling factor
            scaling_factor = lot_size / base_lot_size

            # Scale daily limits proportionally
            daily_profit_limit = base_profit_limit * scaling_factor
            daily_loss_limit = base_loss_limit * scaling_factor

            # Calculate risk per trade (scale with lot size but cap at reasonable limits)
            base_risk = 0.005  # 0.5% for 0.01 lot
            risk_per_trade = min(0.02, base_risk * scaling_factor)  # Cap at 2%

            # Calculate max position size (scale with lot size but cap at reasonable limits)
            base_position = 0.01  # 1% for 0.01 lot
            max_position_size = min(0.05, base_position * scaling_factor)  # Cap at 5%

        else:
            print("📊 Using default settings for auto lot size mode")
            daily_profit_limit = 20.0
            daily_loss_limit = 10.0
            risk_per_trade = 0.005  # 0.5%
            max_position_size = 0.01  # 1%

        # Build complete configuration
        config = {
            "lot_size": lot_config,
            "risk_settings": {
                "max_risk_per_trade": risk_per_trade,
                "max_position_size": max_position_size,
                "daily_profit_limit": daily_profit_limit,
                "daily_loss_limit": daily_loss_limit,
                "use_custom_risk": is_manual,
                "auto_calculated": True
            },
            "trading_preferences": {
                "confirm_trades": False,
                "enable_notifications": True,
                "auto_start_trading": True
            },
            "system_info": {
                "created": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
                "version": "2.1"
            }
        }

        print(f"✅ Daily Profit Limit: ${daily_profit_limit:.2f}")
        print(f"✅ Daily Loss Limit: ${daily_loss_limit:.2f}")
        print(f"✅ Risk per Trade: {risk_per_trade*100:.2f}%")
        print(f"✅ Max Position Size: {max_position_size*100:.2f}%")

        return config

    def _display_calculated_config(self, config: Dict):
        """Display the calculated configuration summary."""
        print("\n📋 CONFIGURATION SUMMARY:")
        print("-" * 35)

        lot_config = config["lot_size"]
        risk_config = config["risk_settings"]

        print(f"🎯 Lot Size Mode: {lot_config['mode'].upper()}")
        if lot_config['mode'] == 'manual':
            print(f"📊 Manual Lot Size: {lot_config['manual_lot_size']}")

        print(f"💰 Daily Profit Limit: ${risk_config['daily_profit_limit']:.2f}")
        print(f"⚠️ Daily Loss Limit: ${risk_config['daily_loss_limit']:.2f}")
        print(f"📈 Risk per Trade: {risk_config['max_risk_per_trade']*100:.2f}%")
        print(f"📊 Max Position Size: {risk_config['max_position_size']*100:.2f}%")
        print(f"🚀 Auto-start Trading: Yes")

        if risk_config.get('auto_calculated'):
            print("\n💡 All settings were automatically calculated based on your lot size choice.")
    
    def _display_current_config(self, config: Dict):
        """Display current configuration."""
        lot_config = config.get("lot_size", {})
        risk_config = config.get("risk_settings", {})

        print(f"   Lot size mode: {lot_config.get('mode', 'auto')}")
        if lot_config.get('mode') == 'manual':
            print(f"   Manual lot size: {lot_config.get('manual_lot_size', 0.01)}")

        if risk_config.get('daily_profit_limit'):
            print(f"   Daily profit limit: ${risk_config.get('daily_profit_limit', 20):.2f}")
        if risk_config.get('daily_loss_limit'):
            print(f"   Daily loss limit: ${risk_config.get('daily_loss_limit', 10):.2f}")

        if risk_config.get('auto_calculated'):
            print("   (Settings auto-calculated from lot size)")

    def load_user_config(self) -> Optional[Dict]:
        """Load user configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info("User configuration loaded successfully")
                return config
            else:
                logger.info("No existing user configuration found")
                return None
        except Exception as e:
            logger.error(f"Error loading user configuration: {e}")
            # Try backup file
            try:
                if os.path.exists(self.backup_config_file):
                    with open(self.backup_config_file, 'r') as f:
                        config = json.load(f)
                    logger.info("User configuration loaded from backup")
                    return config
            except Exception as backup_e:
                logger.error(f"Error loading backup configuration: {backup_e}")
            return None
    
    def save_user_config(self, config: Dict) -> bool:
        """Save user configuration to file with backup."""
        try:
            # Create backup of existing config
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    backup_config = json.load(f)
                with open(self.backup_config_file, 'w') as f:
                    json.dump(backup_config, f, indent=2)
            
            # Save new config
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info("User configuration saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving user configuration: {e}")
            return False
    
    def get_lot_size_config(self) -> Dict:
        """Get current lot size configuration."""
        config = self.load_user_config()
        if config and "lot_size" in config:
            return config["lot_size"]
        return self.default_config["lot_size"]
    
    def get_manual_lot_size(self) -> Optional[float]:
        """Get manual lot size if configured."""
        lot_config = self.get_lot_size_config()
        if lot_config.get("mode") == "manual":
            return lot_config.get("manual_lot_size")
        return None

    def update_system_config(self, user_config: Dict) -> bool:
        """Update the system config.py file with user's calculated settings."""
        try:
            risk_settings = user_config.get("risk_settings", {})

            # Read current config.py
            config_file = "config.py"
            if not os.path.exists(config_file):
                logger.warning("config.py not found, cannot update system settings")
                return False

            with open(config_file, 'r') as f:
                config_content = f.read()

            # Update daily limits in SYNTHETIC_RISK_RULES
            daily_profit = risk_settings.get("daily_profit_limit", 20.0)
            daily_loss = risk_settings.get("daily_loss_limit", 10.0)

            # Replace daily profit limit
            import re
            config_content = re.sub(
                r'"max_daily_profit":\s*[\d.]+',
                f'"max_daily_profit": {daily_profit}',
                config_content
            )

            # Replace daily loss limit
            config_content = re.sub(
                r'"max_daily_drawdown":\s*[\d.]+',
                f'"max_daily_drawdown": {daily_loss}',
                config_content
            )

            # Replace risk per trade
            risk_per_trade = risk_settings.get("max_risk_per_trade", 0.005)
            config_content = re.sub(
                r'"base_risk_per_trade":\s*[\d.]+',
                f'"base_risk_per_trade": {risk_per_trade}',
                config_content
            )

            # Replace max position size
            max_position = risk_settings.get("max_position_size", 0.01)
            config_content = re.sub(
                r'"max_position_size":\s*[\d.]+',
                f'"max_position_size": {max_position}',
                config_content
            )

            # Write updated config
            with open(config_file, 'w') as f:
                f.write(config_content)

            logger.info("System configuration updated successfully")
            print("✅ System configuration files updated")
            return True

        except Exception as e:
            logger.error(f"Error updating system config: {e}")
            print(f"⚠️ Could not update system config: {e}")
            return False

def main():
    """Test the user configuration manager."""
    manager = UserConfigManager()
    success = manager.prompt_user_configuration()
    if success:
        print("\n✅ Configuration completed successfully!")
    else:
        print("\n❌ Configuration failed!")

if __name__ == "__main__":
    main()
