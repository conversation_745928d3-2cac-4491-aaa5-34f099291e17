"""
Trading Signal Generator for Synthetic DEX 900 DOWN Index Trading System.
Converts AI model predictions into actionable trading signals with risk management.
"""

import logging
import numpy as np
import pandas as pd
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import config

# Set up logging
logger = logging.getLogger("TradingSignalGenerator")

class SignalType(Enum):
    """Trading signal types."""
    NO_SIGNAL = 0
    WEAK_BUY = 1
    STRONG_BUY = 2
    WEAK_SELL = -1
    STRONG_SELL = -2

class SignalConfidence(Enum):
    """Signal confidence levels."""
    VERY_LOW = 0.0
    LOW = 0.3
    MEDIUM = 0.5
    HIGH = 0.7
    VERY_HIGH = 0.9

@dataclass
class TradingSignal:
    """Trading signal with all necessary information."""
    signal_type: SignalType
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    risk_reward_ratio: float
    timeframe: int
    timestamp: datetime
    ai_predictions: Dict
    market_regime: str
    reasoning: str

class TradingSignalGenerator:
    """
    Generates trading signals from AI model predictions with comprehensive risk management.
    Optimized for DEX 900 DOWN Index synthetic trading.
    """
    
    def __init__(self, ai_manager, pattern_detector, data_collector, trading_engine=None):
        """Initialize the Trading Signal Generator."""
        self.ai_manager = ai_manager
        self.pattern_detector = pattern_detector
        self.data_collector = data_collector
        self._trading_engine = trading_engine  # Reference to trading engine for direct execution
        
        # Signal generation parameters
        self.min_confidence = config.MIN_SIGNAL_CONFIDENCE
        self.min_consensus_strength = 0.4  # Require 40% model agreement (AGGRESSIVE)
        self.max_position_size = config.MAX_POSITION_SIZE
        self.base_risk_per_trade = config.BASE_RISK_PER_TRADE
        
        # Risk management parameters (REMOVED DAILY TRADE LIMITS)
        # self.max_daily_trades = config.MAX_DAILY_TRADES  # REMOVED: No daily trade limits
        self.max_concurrent_positions = config.MAX_CONCURRENT_POSITIONS
        self.min_risk_reward = config.MIN_RISK_REWARD_RATIO
        
        # Synthetic-specific parameters
        self.jump_detection_threshold = 0.005  # 0.5% for jump detection
        self.volatility_expansion_threshold = 0.01  # 1% for volatility expansion
        self.regime_confidence_multiplier = {
            'QUIET': 0.8,      # Lower confidence in quiet periods
            'PRE_JUMP': 1.2,   # Higher confidence before jumps
            'JUMPING': 1.5,    # Highest confidence during jumps
            'POST_JUMP': 1.0,  # Normal confidence after jumps
            'REVERTING': 0.9   # Slightly lower confidence during reversion
        }
        
        # Signal tracking
        self.recent_signals = []
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()
        
        logger.info("Trading Signal Generator initialized")
        
    def generate_signal(self, current_price: float, timeframe: int = 15) -> Optional[TradingSignal]:
        """Generate a trading signal based on current market conditions."""
        try:
            # Reset daily counters if new day
            self._reset_daily_counters()
            
            # Check if we can trade
            if not self._can_generate_signal():
                return None
                
            # Get latest market data
            market_data = self.data_collector.get_latest_data(timeframe, count=100)
            if market_data.empty:
                logger.warning("No market data available for signal generation")
                return None
                
            # Calculate synthetic indicators
            df_indicators = self.pattern_detector.calculate_synthetic_indicators(market_data)
            latest_data = df_indicators.iloc[-1]
            
            # Get AI ensemble prediction
            model_features = self._extract_features_for_ai(df_indicators)
            if not model_features:
                logger.warning("Could not extract features for AI prediction")
                return None

            # Use timeframe-specific ensemble voting (5-minute target for current trading cycle)
            ensemble_prediction = self.ai_manager.get_ensemble_prediction_with_features(model_features, target_timeframe=5)

            # SAVE PREDICTIONS TO SHARED CACHE for dashboard synchronization
            self._save_predictions_to_shared_cache(model_features, ensemble_prediction)

            # Import decision logger
            from model_decision_logger import decision_logger

            # Check for strong signals that should trigger immediate trades
            strong_signals = ensemble_prediction.get('strong_signals', [])  # Ultra-strong (±2)
            timeframe_consensus_signals = ensemble_prediction.get('timeframe_consensus_signals', [])  # Consensus (±1)

            # CONFIDENCE-BASED TRADING LOGIC - PROCESS ALL STRONG SIGNALS
            signals_processed = 0
            first_signal = None  # Initialize for backward compatibility

            if strong_signals or timeframe_consensus_signals:
                # Strong signal (2/-2) detected - process ALL strong signals
                for strong_signal in strong_signals:
                    logger.info(f"STRONG SIGNAL DETECTED: {strong_signal['model_name']} - Signal: {strong_signal['signal']} (Confidence: {strong_signal['confidence']:.3f})")

                    # Create immediate trading signal based on ULTRA-STRONG model prediction (±2 only)
                    if strong_signal['signal'] == 2:
                        signal_type = SignalType.STRONG_BUY
                    elif strong_signal['signal'] == -2:
                        signal_type = SignalType.STRONG_SELL
                    else:
                        # This should never happen as strong_signals should only contain ±2
                        logger.error(f"Invalid strong signal strength: {strong_signal['signal']} (expected ±2)")
                        continue

                    # Calculate position size and risk management
                    regime_analysis = self._analyze_market_regime(latest_data, df_indicators)
                    position_size = self._calculate_position_size(strong_signal['confidence'], regime_analysis)

                    # Determine timeframe group from signal context
                    timeframe_group = self._get_timeframe_group_from_signal(strong_signal)
                    stop_loss, take_profit = self._calculate_stop_loss_take_profit(signal_type, current_price, regime_analysis, df_indicators, timeframe_group)

                    # Calculate risk-reward ratio
                    if signal_type == SignalType.STRONG_BUY:
                        risk_reward = (take_profit - current_price) / (current_price - stop_loss)
                    else:  # STRONG_SELL
                        risk_reward = (current_price - take_profit) / (stop_loss - current_price)

                    # Create trading signal
                    trading_signal = TradingSignal(
                        signal_type=signal_type,
                        confidence=strong_signal['confidence'],
                        entry_price=current_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        position_size=position_size,
                        risk_reward_ratio=risk_reward,
                        timeframe=5,  # 5-minute timeframe for strong signals
                        timestamp=pd.Timestamp.now(),
                        ai_predictions=ensemble_prediction,
                        market_regime=str(regime_analysis),
                        reasoning=f"Strong signal ({strong_signal['signal']}) from {strong_signal['model_name']} ({strong_signal['timeframe_category']}) with {strong_signal['confidence']:.3f} confidence. Pattern: {strong_signal['purpose']}"
                    )

                    # Log trade trigger
                    decision_logger.log_trade_trigger({
                        "trigger_type": "STRONG_SIGNAL",
                        "triggering_model": strong_signal['model_name'],
                        "signal": strong_signal['signal'],
                        "confidence": strong_signal['confidence'],
                        "entry_price": current_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "position_size": position_size,
                        "reason": f"Strong signal ({strong_signal['signal']}) from {strong_signal['model_name']} with {strong_signal['confidence']:.3f} confidence"
                    })

                    logger.info(f"IMMEDIATE TRADE SIGNAL GENERATED: {signal_type.name}")

                    # EXECUTE SIGNAL IMMEDIATELY - Don't return, process all signals
                    try:
                        # Import trading engine to execute signal directly
                        if hasattr(self, '_trading_engine') and self._trading_engine:
                            self._trading_engine._execute_signal(trading_signal)
                            signals_processed += 1
                        else:
                            # Fallback: Store first signal if no direct execution available
                            if first_signal is None:
                                first_signal = trading_signal
                            signals_processed += 1
                    except Exception as e:
                        logger.error(f"Error executing strong signal: {e}")
                        # Continue processing other signals

                # If we processed strong signals and have direct execution, return None (signals already executed)
                if signals_processed > 0 and hasattr(self, '_trading_engine') and self._trading_engine:
                    logger.info(f"Processed {signals_processed} strong signals directly")
                    return None

                # If no direct execution available, return the first signal for backward compatibility
                if signals_processed > 0 and first_signal is not None:
                    return first_signal

                # Check timeframe consensus signals (±1 with confidence ≥0.7, need 2+ models)
                for consensus_signal in timeframe_consensus_signals:
                    logger.info(f"🤝 TIMEFRAME CONSENSUS DETECTED: {consensus_signal['model_name']} - Signal: {consensus_signal['signal']} (Confidence: {consensus_signal['confidence']:.3f})")

                    # Create immediate trading signal based on timeframe consensus (±1 only)
                    if consensus_signal['signal'] == 1:
                        signal_type = SignalType.WEAK_BUY
                    elif consensus_signal['signal'] == -1:
                        signal_type = SignalType.WEAK_SELL
                    else:
                        # This should never happen as consensus_signals should only contain ±1
                        logger.error(f"Invalid consensus signal strength: {consensus_signal['signal']} (expected ±1)")
                        continue

                    # Calculate position size and risk management
                    regime_analysis = self._analyze_market_regime(latest_data, df_indicators)
                    position_size = self._calculate_position_size(consensus_signal['confidence'], regime_analysis)

                    # Determine timeframe group from consensus signal
                    timeframe_group = self._get_timeframe_group_from_signal(consensus_signal)
                    stop_loss, take_profit = self._calculate_stop_loss_take_profit(signal_type, current_price, regime_analysis, df_indicators, timeframe_group)

                    # Calculate risk-reward ratio
                    if signal_type == SignalType.WEAK_BUY:
                        risk_reward = (take_profit - current_price) / (current_price - stop_loss)
                    else:  # WEAK_SELL
                        risk_reward = (current_price - take_profit) / (stop_loss - current_price)

                    # Create trading signal
                    trading_signal = TradingSignal(
                        signal_type=signal_type,
                        confidence=consensus_signal['confidence'],
                        entry_price=current_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        position_size=position_size,
                        risk_reward_ratio=risk_reward,
                        timeframe=5,  # 5-minute timeframe for consensus signals
                        timestamp=pd.Timestamp.now(),
                        ai_predictions=ensemble_prediction,
                        market_regime=str(regime_analysis),
                        reasoning=f"Timeframe consensus signal ({consensus_signal['signal']}) from {consensus_signal['timeframe_category']} models with {consensus_signal['confidence']:.3f} confidence. Pattern: {consensus_signal['purpose']}"
                    )

                    # Log trade trigger
                    decision_logger.log_trade_trigger({
                        "trigger_type": "TIMEFRAME_CONSENSUS",
                        "triggering_model": consensus_signal['model_name'],
                        "signal": consensus_signal['signal'],
                        "confidence": consensus_signal['confidence'],
                        "entry_price": current_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "position_size": position_size,
                        "reason": f"Timeframe consensus signal ({consensus_signal['signal']}) from {consensus_signal['timeframe_category']} models with {consensus_signal['confidence']:.3f} confidence"
                    })

                    logger.info(f"✅ TIMEFRAME CONSENSUS TRADE SIGNAL GENERATED: {signal_type.name}")
                    return trading_signal

            # Analyze market regime for ensemble-based decisions
            regime_analysis = self._analyze_market_regime(latest_data, df_indicators)

            # Generate signal based on ensemble prediction and market conditions (for weaker signals)
            signal = self._create_trading_signal(
                ensemble_prediction=ensemble_prediction,
                regime_analysis=regime_analysis,
                current_price=current_price,
                market_data=df_indicators,
                timeframe=timeframe
            )
            
            if signal:
                self._track_signal(signal)
                logger.info(f"Generated {signal.signal_type.name} signal with {signal.confidence:.3f} confidence")
                
            return signal
            
        except Exception as e:
            logger.error(f"Error generating trading signal: {e}")
            return None
            
    def _extract_features_for_ai(self, df_indicators: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Extract features for AI model prediction - returns features for each model."""
        try:
            if len(df_indicators) < 20:
                return {}

            latest_row = df_indicators.iloc[-1]

            if not self.ai_manager.scalers:
                logger.warning("No trained AI models available")
                return {}

            # Extract features for each trained model
            model_features = {}

            for model_name in self.ai_manager.scalers.keys():
                try:
                    features = self._extract_features_for_model(df_indicators, model_name, latest_row)
                    if features is not None:
                        model_features[model_name] = features
                except Exception as e:
                    logger.error(f"Error extracting features for {model_name}: {e}")

            return model_features

        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return {}

    def _extract_features_for_model(self, df_indicators: pd.DataFrame, model_name: str, latest_row: pd.Series) -> Optional[np.ndarray]:
        """Extract features for a specific model based on its configuration."""
        try:
            # Get model configuration
            model_config = self.ai_manager.model_configs.get(model_name, {})
            feature_types = model_config.get("features", [])
            required_features = self.ai_manager.scalers[model_name].n_features_in_

            features = []

            # Extract features based on model's feature types
            if "price_action" in feature_types:
                features.extend(self._get_price_action_features(df_indicators, latest_row))

            if "volume" in feature_types:
                features.extend(self._get_volume_features(df_indicators, latest_row))

            if "micro_patterns" in feature_types:
                features.extend(self._get_micro_pattern_features(df_indicators, latest_row))

            if "momentum" in feature_types:
                features.extend(self._get_momentum_features(df_indicators, latest_row))

            if "rsi" in feature_types:
                features.extend(self._get_rsi_features(df_indicators, latest_row))

            if "macd" in feature_types:
                features.extend(self._get_macd_features(df_indicators, latest_row))

            if "volume_velocity" in feature_types:
                features.extend(self._get_volume_velocity_features(df_indicators, latest_row))

            if "deviation" in feature_types:
                features.extend(self._get_deviation_features(df_indicators, latest_row))

            if "bollinger" in feature_types:
                features.extend(self._get_bollinger_features(df_indicators, latest_row))

            if "rsi_extreme" in feature_types:
                features.extend(self._get_rsi_extreme_features(df_indicators, latest_row))

            if "trend_strength" in feature_types:
                features.extend(self._get_trend_strength_features(df_indicators, latest_row))

            if "moving_averages" in feature_types:
                features.extend(self._get_moving_average_features(df_indicators, latest_row))

            if "volume_trend" in feature_types:
                features.extend(self._get_volume_trend_features(df_indicators, latest_row))

            if "support_resistance" in feature_types:
                features.extend(self._get_support_resistance_features(df_indicators, latest_row))

            if "volume_breakout" in feature_types:
                features.extend(self._get_volume_breakout_features(df_indicators, latest_row))

            if "volatility" in feature_types:
                features.extend(self._get_volatility_features(df_indicators, latest_row))

            if "volatility_regime" in feature_types:
                features.extend(self._get_volatility_regime_features(df_indicators, latest_row))

            if "atr" in feature_types:
                features.extend(self._get_atr_features(df_indicators, latest_row))

            if "bollinger_width" in feature_types:
                features.extend(self._get_bollinger_width_features(df_indicators, latest_row))

            # Always add synthetic-specific features
            features.extend(self._get_synthetic_features(df_indicators, latest_row))

            # IMPORTANT: Do NOT pad or trim features - they should match exactly
            # If there's a mismatch, it indicates a bug in feature extraction consistency
            if len(features) != required_features:
                logger.error(f"Feature count mismatch for {model_name}: got {len(features)}, expected {required_features}")
                logger.error(f"Feature types: {feature_types}")
                logger.error(f"Generated features: {len(features)} values")
                return None

            return np.array(features)

        except Exception as e:
            logger.error(f"Error extracting features for model {model_name}: {e}")
            return None

    def _get_price_action_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract price action features."""
        features = []
        if len(df_indicators) >= 10:
            features.extend([
                latest_row['close'] / df_indicators['close'].iloc[-10] - 1,  # 10-period return
                latest_row['high'] / latest_row['close'] - 1,               # High deviation
                latest_row['low'] / latest_row['close'] - 1,                # Low deviation
                df_indicators['volume'].iloc[-5:].mean(),                   # Recent volume
                len(df_indicators[df_indicators['close'] > df_indicators['open']]) / len(df_indicators)  # Bullish ratio
            ])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0, 0.5])
        return features

    def _get_volume_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volume features."""
        features = []
        if len(df_indicators) >= 5:
            vol_mean = df_indicators['volume'].mean()
            vol_recent_mean = df_indicators['volume'].iloc[-5:].mean()
            vol_std = df_indicators['volume'].std()

            # Protect against division by zero and NaN values
            if vol_mean > 0 and not pd.isna(vol_mean) and not pd.isna(vol_recent_mean) and not pd.isna(vol_std):
                vol_ratio = vol_recent_mean / vol_mean - 1
                vol_cv = vol_std / vol_mean
            else:
                vol_ratio = 0.0
                vol_cv = 0.0

            features.extend([vol_ratio, vol_cv])
        else:
            features.extend([0.0, 0.0])
        return features

    def _get_micro_pattern_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract micro pattern features."""
        features = []
        if len(df_indicators) >= 5:
            # Recent price patterns
            recent_highs = df_indicators['high'].iloc[-5:].max()
            recent_lows = df_indicators['low'].iloc[-5:].min()
            features.extend([
                (latest_row['close'] - recent_lows) / (recent_highs - recent_lows) if recent_highs != recent_lows else 0.5,
                (latest_row['high'] - latest_row['low']) / latest_row['close']
            ])
        else:
            features.extend([0.5, 0.0])
        return features

    def _get_momentum_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract momentum features."""
        features = []
        if len(df_indicators) >= 5:
            features.extend([
                latest_row['close'] / df_indicators['close'].iloc[-5] - 1,  # 5-period momentum
                df_indicators['close'].iloc[-3:].mean() / df_indicators['close'].iloc[-6:-3].mean() - 1 if len(df_indicators) >= 6 else 0.0
            ])
        else:
            features.extend([0.0, 0.0])
        return features

    def _get_rsi_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract RSI-like features."""
        features = []
        if len(df_indicators) >= 14:
            returns = df_indicators['close'].pct_change().dropna()
            gains = returns.where(returns > 0, 0)
            losses = -returns.where(returns < 0, 0)
            avg_gain = gains.rolling(14).mean().iloc[-1]
            avg_loss = losses.rolling(14).mean().iloc[-1]
            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 100 if avg_gain > 0 else 50
            features.append(rsi / 100)  # Normalize to 0-1
        else:
            features.append(0.5)
        return features

    def _get_macd_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract MACD-like features."""
        features = []
        if len(df_indicators) >= 26:
            ema12 = df_indicators['close'].ewm(span=12).mean().iloc[-1]
            ema26 = df_indicators['close'].ewm(span=26).mean().iloc[-1]
            macd = (ema12 - ema26) / latest_row['close']
            features.append(macd)
        else:
            features.append(0.0)
        return features

    def _get_volume_velocity_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volume velocity features."""
        features = []
        if 'tick_velocity' in latest_row:
            features.append(latest_row['tick_velocity'])
        else:
            features.append(0.0)
        return features

    def _get_deviation_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract deviation features."""
        features = []
        if len(df_indicators) >= 20:
            mean_price = df_indicators['close'].rolling(20).mean().iloc[-1]
            std_price = df_indicators['close'].rolling(20).std().iloc[-1]
            deviation = (latest_row['close'] - mean_price) / std_price if std_price > 0 else 0
            features.append(deviation)
        else:
            features.append(0.0)
        return features

    def _get_bollinger_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Bollinger Band features."""
        features = []
        if len(df_indicators) >= 20:
            mean_price = df_indicators['close'].rolling(20).mean().iloc[-1]
            std_price = df_indicators['close'].rolling(20).std().iloc[-1]
            upper_band = mean_price + (2 * std_price)
            lower_band = mean_price - (2 * std_price)
            bb_position = (latest_row['close'] - lower_band) / (upper_band - lower_band) if upper_band != lower_band else 0.5
            features.append(bb_position)
        else:
            features.append(0.5)
        return features

    def _get_rsi_extreme_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract RSI extreme features."""
        rsi_features = self._get_rsi_features(df_indicators, latest_row)
        features = []
        if rsi_features:
            rsi = rsi_features[0] * 100  # Convert back to 0-100 scale
            extreme = 1.0 if rsi > 80 or rsi < 20 else 0.0
            features.append(extreme)
        else:
            features.append(0.0)
        return features

    def _get_trend_strength_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract trend strength features."""
        features = []
        if len(df_indicators) >= 20:
            ma_short = df_indicators['close'].rolling(5).mean().iloc[-1]
            ma_long = df_indicators['close'].rolling(20).mean().iloc[-1]
            trend_strength = (ma_short - ma_long) / latest_row['close']
            features.append(trend_strength)
        else:
            features.append(0.0)
        return features

    def _get_moving_average_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract moving average features."""
        features = []
        if len(df_indicators) >= 20:
            ma5 = df_indicators['close'].rolling(5).mean().iloc[-1]
            ma10 = df_indicators['close'].rolling(10).mean().iloc[-1]
            ma20 = df_indicators['close'].rolling(20).mean().iloc[-1]
            features.extend([
                latest_row['close'] / ma5 - 1,
                latest_row['close'] / ma10 - 1,
                latest_row['close'] / ma20 - 1
            ])
        else:
            features.extend([0.0, 0.0, 0.0])
        return features

    def _get_volume_trend_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volume trend features."""
        features = []
        if len(df_indicators) >= 10:
            vol_ma = df_indicators['volume'].rolling(10).mean().iloc[-1]
            current_vol = latest_row['volume']

            # Protect against division by zero and NaN values
            if vol_ma > 0 and not pd.isna(vol_ma) and not pd.isna(current_vol):
                vol_trend = current_vol / vol_ma - 1
            else:
                vol_trend = 0.0

            features.append(vol_trend)
        else:
            features.append(0.0)
        return features

    def _get_support_resistance_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract support/resistance features."""
        features = []
        if len(df_indicators) >= 20:
            high_20 = df_indicators['high'].rolling(20).max().iloc[-1]
            low_20 = df_indicators['low'].rolling(20).min().iloc[-1]
            position = (latest_row['close'] - low_20) / (high_20 - low_20) if high_20 != low_20 else 0.5
            features.append(position)
        else:
            features.append(0.5)
        return features

    def _get_volume_breakout_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volume breakout features."""
        features = []
        if len(df_indicators) >= 20:
            vol_avg = df_indicators['volume'].rolling(20).mean().iloc[-1]
            vol_breakout = latest_row['volume'] / vol_avg if vol_avg > 0 else 1.0
            features.append(vol_breakout)
        else:
            features.append(1.0)
        return features

    def _get_volatility_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility features."""
        features = []
        if 'volatility' in latest_row:
            features.append(latest_row['volatility'])
        else:
            features.append(0.0)
        return features

    def _get_volatility_regime_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract volatility regime features."""
        features = []
        if 'regime_state' in latest_row:
            features.append(latest_row['regime_state'] / 4)  # Normalize to 0-1
        else:
            features.append(0.0)
        return features

    def _get_atr_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract ATR features."""
        features = []
        if len(df_indicators) >= 14:
            high_low = df_indicators['high'] - df_indicators['low']
            high_close = abs(df_indicators['high'] - df_indicators['close'].shift(1))
            low_close = abs(df_indicators['low'] - df_indicators['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
            features.append(atr / latest_row['close'])
        else:
            features.append(0.0)
        return features

    def _get_bollinger_width_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract Bollinger Band width features."""
        features = []
        if len(df_indicators) >= 20:
            std_price = df_indicators['close'].rolling(20).std().iloc[-1]
            mean_price = df_indicators['close'].rolling(20).mean().iloc[-1]
            bb_width = (2 * std_price) / mean_price if mean_price > 0 else 0
            features.append(bb_width)
        else:
            features.append(0.0)
        return features

    def _get_synthetic_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
        """Extract synthetic-specific features."""
        features = []
        synthetic_cols = ['jumpiness_score', 'volatility_compression', 'price_acceleration',
                         'tick_velocity', 'mean_reversion_signal', 'regime_state']

        for col in synthetic_cols:
            if col in latest_row:
                features.append(latest_row[col] if not pd.isna(latest_row[col]) else 0.0)
            else:
                features.append(0.0)

        return features

    def _analyze_market_regime(self, latest_data: pd.Series, df_indicators: pd.DataFrame) -> Dict:
        """Analyze current market regime and conditions."""
        regime_state = latest_data.get('regime_state', 0)
        regime_name = self.pattern_detector.regimes.get(regime_state, 'UNKNOWN')
        
        # Calculate regime-specific metrics
        jumpiness = latest_data.get('jumpiness_score', 0)
        volatility = latest_data.get('volatility', 0)
        compression = latest_data.get('volatility_compression', 0)
        acceleration = latest_data.get('price_acceleration', 0)
        
        # Detect potential jump conditions
        jump_probability = 0.0
        if regime_name == 'PRE_JUMP':
            jump_probability = min(0.8, jumpiness + compression)
        elif regime_name == 'JUMPING':
            jump_probability = 0.9
        elif jumpiness > 0.7:
            jump_probability = jumpiness * 0.6
            
        # Detect volatility expansion
        vol_expansion = False
        if len(df_indicators) >= 20:
            recent_vol = df_indicators['volatility'].iloc[-5:].mean()
            historical_vol = df_indicators['volatility'].iloc[-20:-5].mean()
            if recent_vol > historical_vol * 1.5:
                vol_expansion = True
                
        return {
            'regime_name': regime_name,
            'regime_state': regime_state,
            'jumpiness': jumpiness,
            'volatility': volatility,
            'compression': compression,
            'acceleration': acceleration,
            'jump_probability': jump_probability,
            'volatility_expansion': vol_expansion,
            'confidence_multiplier': self.regime_confidence_multiplier.get(regime_name, 1.0)
        }
        
    def _create_trading_signal(self, ensemble_prediction: Dict, regime_analysis: Dict, 
                             current_price: float, market_data: pd.DataFrame, timeframe: int) -> Optional[TradingSignal]:
        """Create a trading signal from AI prediction and market analysis."""
        try:
            # Extract AI prediction details
            ai_signal = ensemble_prediction.get('ensemble_signal', 0)
            ai_confidence = ensemble_prediction.get('confidence', 0)
            consensus = ensemble_prediction.get('consensus', 'no_models')
            consensus_strength = ensemble_prediction.get('consensus_strength', 0)
            
            # Check minimum requirements
            if ai_confidence < self.min_confidence:
                logger.debug(f"AI confidence {ai_confidence:.3f} below minimum {self.min_confidence}")
                return None
                
            if consensus_strength < self.min_consensus_strength:
                logger.debug(f"Consensus strength {consensus_strength:.3f} below minimum {self.min_consensus_strength}")
                return None
                
            # Apply regime-based confidence adjustment
            regime_multiplier = regime_analysis['confidence_multiplier']
            adjusted_confidence = min(1.0, ai_confidence * regime_multiplier)
            
            # Determine signal type based on AI prediction and regime
            signal_type = self._determine_signal_type(ai_signal, adjusted_confidence, regime_analysis)
            
            if signal_type == SignalType.NO_SIGNAL:
                return None
                
            # Calculate position sizing
            position_size = self._calculate_position_size(adjusted_confidence, regime_analysis)
            
            # Calculate stop loss and take profit (default to medium term for ensemble signals)
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                signal_type, current_price, regime_analysis, market_data, 'medium_term'
            )
            
            # Calculate risk-reward ratio
            if signal_type.value > 0:  # Buy signal
                risk = current_price - stop_loss
                reward = take_profit - current_price
            else:  # Sell signal
                risk = stop_loss - current_price
                reward = current_price - take_profit
                
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # Check minimum risk-reward ratio
            if risk_reward_ratio < self.min_risk_reward:
                logger.debug(f"Risk-reward ratio {risk_reward_ratio:.2f} below minimum {self.min_risk_reward}")
                return None

            # Create reasoning
            reasoning = self._generate_reasoning(ai_signal, regime_analysis, ensemble_prediction)

            # Create trading signal
            signal = TradingSignal(
                signal_type=signal_type,
                confidence=adjusted_confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                risk_reward_ratio=risk_reward_ratio,
                timeframe=timeframe,
                timestamp=datetime.now(),
                ai_predictions=ensemble_prediction,
                market_regime=regime_analysis['regime_name'],
                reasoning=reasoning
            )

            return signal

        except Exception as e:
            logger.error(f"Error creating trading signal: {e}")
            return None

    def _determine_signal_type(self, ai_signal: int, confidence: float, regime_analysis: Dict) -> SignalType:
        """Determine the trading signal type based on AI prediction and market regime."""
        # No signal for very low confidence
        if confidence < 0.4:
            return SignalType.NO_SIGNAL

        # Adjust signal strength based on regime
        regime_name = regime_analysis['regime_name']
        jump_probability = regime_analysis['jump_probability']

        # Strong signals during high-probability jump conditions
        if jump_probability > 0.7:
            if ai_signal >= 1:
                return SignalType.STRONG_BUY
            elif ai_signal <= -1:
                return SignalType.STRONG_SELL

        # Normal signal determination
        if ai_signal == 2:
            return SignalType.STRONG_BUY if confidence > 0.7 else SignalType.WEAK_BUY
        elif ai_signal == 1:
            return SignalType.WEAK_BUY
        elif ai_signal == -1:
            return SignalType.WEAK_SELL
        elif ai_signal == -2:
            return SignalType.STRONG_SELL if confidence > 0.7 else SignalType.WEAK_SELL
        else:
            return SignalType.NO_SIGNAL

    def _calculate_position_size(self, confidence: float, regime_analysis: Dict) -> float:
        """Calculate position size based on confidence and market conditions."""
        # Base position size from confidence
        base_size = self.base_risk_per_trade * confidence

        # Adjust for regime
        regime_name = regime_analysis['regime_name']
        if regime_name == 'JUMPING':
            base_size *= 1.5  # Increase size during jumps
        elif regime_name == 'QUIET':
            base_size *= 0.7  # Reduce size during quiet periods

        # Adjust for volatility
        volatility = regime_analysis['volatility']
        if volatility > 0.02:  # High volatility
            base_size *= 0.8
        elif volatility < 0.005:  # Low volatility
            base_size *= 1.2

        # Cap at maximum position size
        return min(base_size, self.max_position_size)

    def _calculate_stop_loss_take_profit(self, signal_type: SignalType, current_price: float,
                                       regime_analysis: Dict, market_data: pd.DataFrame,
                                       timeframe_group: Optional[str] = None) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels with timeframe-specific distances."""

        # TIMEFRAME-SPECIFIC SL/TP DISTANCES (in points)
        # Updated per user request: Much tighter SL/TP values
        # SPECIAL SELL TRADE SETTINGS per user request

        # Check if this is a SELL trade
        is_sell_trade = signal_type.value < 0

        if is_sell_trade:
            # CUSTOM SL/TP FOR SELL TRADES ONLY (per user request)
            timeframe_distances = {
                'short_term': {
                    'sl_distance': 100.0,     # 100 points SL for sell trades
                    'tp_distance': 50.0,      # 50 points TP for sell trades (updated per user request)
                    'min_distance': 50.0      # Minimum required distance
                },
                'medium_term': {
                    'sl_distance': 150.0,     # 150 points SL for sell trades
                    'tp_distance': 100.0,     # 100 points TP for sell trades
                    'min_distance': 100.0     # Minimum required distance
                },
                'long_term': {
                    'sl_distance': 300.0,     # 300 points SL for sell trades
                    'tp_distance': 100.0,     # 100 points TP for sell trades
                    'min_distance': 100.0     # Minimum required distance
                }
            }
        else:
            # ORIGINAL BUY TRADE SETTINGS (unchanged)
            timeframe_distances = {
                'short_term': {
                    'sl_distance': 50.0,      # 50 points SL for quick scalping
                    'tp_distance': 100.0,     # 100 points TP for quick profit
                    'min_distance': 50.0      # Minimum required distance
                },
                'medium_term': {
                    'sl_distance': 200.0,     # 200 points SL for swing trading
                    'tp_distance': 400.0,     # 400 points TP for medium targets
                    'min_distance': 200.0     # Minimum required distance
                },
                'long_term': {
                    'sl_distance': 300.0,     # 300 points SL for trend following
                    'tp_distance': 600.0,     # 600 points TP for larger targets
                    'min_distance': 300.0     # Minimum required distance
                }
            }

        # Default to medium term if timeframe_group not specified
        if timeframe_group not in timeframe_distances:
            timeframe_group = 'medium_term'

        distances = timeframe_distances[timeframe_group]
        sl_distance = distances['sl_distance']
        tp_distance = distances['tp_distance']
        min_distance = distances['min_distance']

        logger.debug(f"Using {timeframe_group} SL/TP: SL={sl_distance}pts, TP={tp_distance}pts")

        # Adjust distances based on volatility
        volatility = regime_analysis['volatility']
        volatility_multiplier = 1.0
        if volatility > 0.02:
            volatility_multiplier = 1.3  # Wider stops in high volatility
        elif volatility < 0.005:
            volatility_multiplier = 0.8  # Tighter stops in low volatility

        # Adjust for regime
        regime_name = regime_analysis['regime_name']
        regime_multiplier = 1.0
        if regime_name == 'JUMPING':
            regime_multiplier = 1.2  # Wider stops during jumps
        elif regime_name == 'QUIET':
            regime_multiplier = 0.9  # Slightly tighter stops during quiet periods

        # Apply adjustments to distances
        adjusted_sl_distance = sl_distance * volatility_multiplier * regime_multiplier
        adjusted_tp_distance = tp_distance * volatility_multiplier * regime_multiplier

        # Ensure minimum distance requirements
        min_required = distances['min_distance']
        adjusted_sl_distance = max(adjusted_sl_distance, min_required)
        adjusted_tp_distance = max(adjusted_tp_distance, min_required)

        # Calculate stop loss and take profit based on signal direction
        if signal_type.value > 0:  # Buy signal
            stop_loss = current_price - adjusted_sl_distance
            take_profit = current_price + adjusted_tp_distance
        else:  # Sell signal
            stop_loss = current_price + adjusted_sl_distance
            take_profit = current_price - adjusted_tp_distance

        logger.debug(f"Final SL/TP for {timeframe_group}: SL={stop_loss:.2f} ({adjusted_sl_distance:.1f}pts), TP={take_profit:.2f} ({adjusted_tp_distance:.1f}pts)")

        return stop_loss, take_profit

    def _get_timeframe_group_from_signal(self, signal_data: Dict) -> str:
        """Determine timeframe group from signal data."""
        # Check if signal has timeframe_category
        if 'timeframe_category' in signal_data:
            return signal_data['timeframe_category']

        # Check model name for timeframe hints
        model_name = signal_data.get('model_name', '').lower()
        if any(term in model_name for term in ['short', 'pattern', 'momentum']):
            return 'short_term'
        elif any(term in model_name for term in ['medium', 'breakout', 'trend']):
            return 'medium_term'
        elif any(term in model_name for term in ['long', 'portfolio', 'volatility']):
            return 'long_term'

        # Default to medium term
        return 'medium_term'

    def _generate_reasoning(self, ai_signal: int, regime_analysis: Dict, ensemble_prediction: Dict) -> str:
        """Generate human-readable reasoning for the trading signal."""
        reasoning_parts = []

        # AI prediction reasoning
        confidence = ensemble_prediction.get('confidence', 0)
        consensus = ensemble_prediction.get('consensus', 'unknown')
        total_models = ensemble_prediction.get('total_models', 0)

        reasoning_parts.append(f"AI Ensemble: {ai_signal} signal with {confidence:.1%} confidence")
        reasoning_parts.append(f"Model consensus: {consensus} ({total_models} models)")

        # Regime reasoning
        regime_name = regime_analysis['regime_name']
        jump_prob = regime_analysis['jump_probability']
        reasoning_parts.append(f"Market regime: {regime_name}")

        if jump_prob > 0.5:
            reasoning_parts.append(f"Jump probability: {jump_prob:.1%}")

        # Volatility reasoning
        if regime_analysis['volatility_expansion']:
            reasoning_parts.append("Volatility expansion detected")

        # Jumpiness reasoning
        jumpiness = regime_analysis['jumpiness']
        if jumpiness > 0.7:
            reasoning_parts.append(f"High jumpiness: {jumpiness:.1%}")

        return " | ".join(reasoning_parts)

    def _can_generate_signal(self) -> bool:
        """Check if we can generate a new signal based on risk limits."""
        # REMOVED: Daily trade limit check - No limits on number of trades per day
        # REMOVED: if self.daily_trade_count >= self.max_daily_trades:
        #     logger.debug(f"Daily trade limit reached: {self.daily_trade_count}/{self.max_daily_trades}")
        #     return False

        # Check recent signal frequency (prevent over-trading)
        now = datetime.now()
        recent_signals = [s for s in self.recent_signals if (now - s.timestamp).seconds < 300]  # 5 minutes
        if len(recent_signals) >= 2:
            logger.debug("Too many recent signals, waiting...")
            return False

        # CRITICAL: Prevent rapid signal generation (minimum 30 seconds between signals)
        if self.recent_signals:
            last_signal_time = self.recent_signals[-1].timestamp
            time_since_last = (now - last_signal_time).total_seconds()
            if time_since_last < 30:  # 30 second minimum between signals
                logger.debug(f"Signal too soon after last signal: {time_since_last:.1f}s < 30s")
                return False

        return True

    def _save_predictions_to_shared_cache(self, model_features: Dict[str, np.ndarray], ensemble_prediction: Dict):
        """Save current model predictions to shared cache for dashboard synchronization."""
        try:
            # Create data directory if it doesn't exist
            os.makedirs("data", exist_ok=True)

            # Generate individual predictions for each model
            predictions_cache = {}

            for model_name, features in model_features.items():
                try:
                    prediction = self.ai_manager.predict(model_name, features)
                    if prediction:
                        predictions_cache[model_name] = {
                            "signal": prediction["signal"],
                            "confidence": prediction["confidence"],
                            "model_type": prediction["model_type"],
                            "timestamp": datetime.now().strftime('%H:%M:%S')
                        }
                except Exception as e:
                    logger.warning(f"Error getting prediction for {model_name}: {e}")

            # Create cache data structure
            cache_data = {
                "timestamp": datetime.now().isoformat(),
                "predictions": predictions_cache,
                "ensemble_data": {
                    "ensemble_signal": ensemble_prediction.get("ensemble_signal", 0),
                    "confidence": ensemble_prediction.get("confidence", 0.0),
                    "consensus": ensemble_prediction.get("consensus", "unknown"),
                    "strong_signals": ensemble_prediction.get("strong_signals", []),
                    "timeframe_consensus_signals": ensemble_prediction.get("timeframe_consensus_signals", [])
                }
            }

            # Save to shared cache file
            cache_file = "data/shared_predictions_cache.json"
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.debug(f"Saved {len(predictions_cache)} predictions to shared cache")

        except Exception as e:
            logger.warning(f"Error saving predictions to shared cache: {e}")

    def _track_signal(self, signal: TradingSignal):
        """Track generated signals for analysis and limits."""
        self.recent_signals.append(signal)
        self.daily_trade_count += 1

        # Keep only recent signals (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.recent_signals = [s for s in self.recent_signals if s.timestamp > cutoff_time]

    def _reset_daily_counters(self):
        """Reset daily counters if new day."""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_trade_count = 0
            self.last_reset_date = current_date
            logger.info("Daily trade counters reset")

    def get_signal_statistics(self) -> Dict:
        """Get statistics about recent signals."""
        if not self.recent_signals:
            return {"total_signals": 0}

        recent_24h = [s for s in self.recent_signals if (datetime.now() - s.timestamp).hours < 24]

        signal_types = {}
        confidences = []
        risk_rewards = []

        for signal in recent_24h:
            signal_type = signal.signal_type.name
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
            confidences.append(signal.confidence)
            risk_rewards.append(signal.risk_reward_ratio)

        return {
            "total_signals": len(recent_24h),
            "daily_count": self.daily_trade_count,
            "signal_types": signal_types,
            "avg_confidence": np.mean(confidences) if confidences else 0,
            "avg_risk_reward": np.mean(risk_rewards) if risk_rewards else 0,
            "max_confidence": max(confidences) if confidences else 0,
            "min_confidence": min(confidences) if confidences else 0
        }
