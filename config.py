"""
Configuration settings for the Synthetic DEX 900 DOWN Index AI Trading System.
Optimized for algorithmic pattern recognition and ultra-fast execution.
"""

import os
from datetime import datetime, timedelta

# System Information
SYSTEM_NAME = "Synthetic DEX 900 DOWN AI Trading System"
VERSION = "2.0"
START_DATE = datetime(2023, 4, 1)  # Historical data start date

# Data collection settings
DATA_SOURCE = "mt5"  # Primary source: MetaTrader 5
SYMBOL = "DEX 900 DOWN Index"  # Der<PERSON>'s synthetic symbol
DATA_CACHE_DIR = "data/synthetic_cache"
SYNTHETIC_CACHE_DIR = "data/synthetic_cache"  # Alias for compatibility
HISTORICAL_DATA_DIR = "data/historical"
REAL_TIME_DATA_DIR = "data/realtime"

# Synthetic-specific timeframes for pattern recognition
SYNTHETIC_TIMEFRAMES = {
    "tick": {
        "name": "Tick Data",
        "interval": "tick",  # Raw tick data
        "lookback_periods": 10000,  # Last 10k ticks
        "purpose": "Micro-pattern detection"
    },
    "scalping": {
        "name": "Scalping",
        "intervals": [1],  # 1-minute only
        "lookback_periods": 1440,  # 24 hours of 1-min data
        "purpose": "Quick entry/exit patterns"
    },
    "short_term": {
        "name": "Short-term Pattern",
        "intervals": [1, 5, 15],  # 1min, 5min, 15min
        "lookback_periods": 500,  # More data for pattern recognition
        "purpose": "Jump/drop prediction"
    },
    "medium_term": {
        "name": "Medium-term Regime",
        "intervals": [15, 30, 60],  # 15min, 30min, 1hr
        "lookback_periods": 200,
        "purpose": "Volatility regime detection"
    },
    "long_term": {
        "name": "Long-term Context",
        "intervals": [60, 240, 1440],  # 1hr, 4hr, 1day
        "lookback_periods": 100,
        "purpose": "Overall algorithmic behavior"
    }
}

# Synthetic-specific indicators and features
SYNTHETIC_INDICATORS = {
    # Volatility Event Detection
    "jumpiness_score": {
        "description": "Measures likelihood of imminent jump",
        "lookback": 50,
        "threshold": 0.7
    },
    "volatility_compression": {
        "description": "Detects quiet periods before events",
        "lookback": 100,
        "compression_ratio": 0.3
    },
    "price_acceleration": {
        "description": "Rate of price change acceleration",
        "lookback": 20,
        "smoothing": 3
    },
    "tick_velocity": {
        "description": "Speed of price movements",
        "lookback": 30,
        "velocity_threshold": 2.0
    },
    
    # Pattern Recognition
    "sequence_similarity": {
        "description": "Matches current pattern to historical",
        "pattern_length": 20,
        "similarity_threshold": 0.8
    },
    "algorithmic_signature": {
        "description": "Detects Deriv's algo fingerprints",
        "signature_length": 50,
        "confidence_threshold": 0.75
    },
    "threshold_proximity": {
        "description": "Distance to potential trigger levels",
        "level_detection_period": 200,
        "proximity_threshold": 0.1
    },

    # EMA/SMA Distance Filter
    "ema_sma_distance": {
        "description": "Prevents trading when EMA20/SMA50 are too close",
        "min_distance_pct": 0.15,
        "enabled": True
    },
    
    # Mean Reversion
    "jump_exhaustion": {
        "description": "Measures if jump move is complete",
        "exhaustion_lookback": 10,
        "momentum_threshold": 0.3
    },
    "reversion_probability": {
        "description": "Likelihood of price returning",
        "reversion_period": 30,
        "probability_threshold": 0.6
    }
}

# Trading Signal Parameters (for compatibility)
BASE_RISK_PER_TRADE = 0.005      # 0.5% risk per trade
MAX_POSITION_SIZE = 0.01          # 1% maximum position size
MIN_SIGNAL_CONFIDENCE = 0.4       # Minimum confidence for signal generation (AGGRESSIVE)
MIN_RISK_REWARD_RATIO = 1.1       # Minimum risk-reward ratio (AGGRESSIVE)
# REMOVED: Daily trade limits - No limits on number of trades per day
# MAX_DAILY_TRADES = 45             # REMOVED: No daily trade limits
# MAX_DAILY_TRADES_PER_TIMEFRAME = {   # REMOVED: No timeframe trade limits
#     'short_term': 20,    # REMOVED
#     'medium_term': 20,   # REMOVED
#     'long_term': 5       # REMOVED
# }
MAX_CONCURRENT_POSITIONS = 3      # Maximum concurrent positions (KEPT)

# Ultra-aggressive risk management for synthetic trading
SYNTHETIC_RISK_RULES = {
    "position_sizing": {
        "base_risk_per_trade": 0.01,  # 0.5% max per trade
        "quiet_period_multiplier": 1.0,
        "pre_jump_multiplier": 0.5,    # Reduce size before jumps
        "jump_period_multiplier": 0.3,  # Tiny size during jumps
        "post_jump_multiplier": 0.7,   # Moderate size for reversions
        "max_position_size": 0.02      # Never risk more than 1%
    },
    
    "stop_loss_rules": {
        "quiet_period_stop_atr_multiplier": 1.5,
        "pre_jump_stop_atr_multiplier": 1.0,     # Tighter stops
        "jump_period_stop_atr_multiplier": 0.5,  # Very tight
        "max_hold_time_seconds": 300,            # 5 minutes max
        "emergency_stop_loss": 0.02              # 2% emergency stop
    },
    
    "profit_taking": {
        "quick_profit_atr_multiplier": 0.5,      # Quick small profits
        "scale_out_levels": [0.25, 0.50, 0.75], # Take profits in stages
        "trailing_stop_atr_multiplier": 0.3,
        "time_profit_seconds": 120               # Take profit after 2 minutes
    },
    
    "circuit_breakers": {
        "max_consecutive_losses": 5,             # Increased from 3 to 5
        "max_daily_drawdown": 20.0,              # $10 daily loss limit
        "max_daily_profit": 40.0,                # $20 daily win limit
        "volatility_shutdown_multiplier": 3.0,   # Stop if volatility exceeds 3x normal
        "pattern_failure_threshold": 0.3         # Pause if pattern recognition fails
    }
}

# Model settings optimized for synthetic data
SYNTHETIC_MODEL_SETTINGS = {
    "regime_detector": {
        "model_type": "lstm_attention",
        "sequence_length": 100,
        "hidden_units": [128, 64, 32],
        "attention_heads": 8,
        "dropout": 0.2,
        "learning_rate": 0.001
    },
    
    "jump_predictor": {
        "model_type": "transformer",
        "sequence_length": 50,
        "d_model": 128,
        "num_heads": 8,
        "num_layers": 4,
        "dropout": 0.1,
        "learning_rate": 0.0005
    },
    
    "mean_reversion_detector": {
        "model_type": "xgboost",
        "max_depth": 6,
        "n_estimators": 200,
        "learning_rate": 0.1,
        "subsample": 0.8,
        "colsample_bytree": 0.8
    },
    
    "scalping_entry": {
        "model_type": "cnn",
        "filters": [32, 64, 128],
        "kernel_sizes": [3, 5, 7],
        "pool_sizes": [2, 2, 2],
        "dropout": 0.3,
        "learning_rate": 0.001
    },
    
    "reinforcement_trader": {
        "algorithm": "ppo",
        "learning_rate": 0.0003,
        "batch_size": 64,
        "n_steps": 2048,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2
    }
}

# Execution settings for 179ms latency
EXECUTION_SETTINGS = {
    "latency_ms": 179,  # Demo account latency
    "order_timeout_ms": 5000,  # 5 second timeout
    "slippage_tolerance": 0.0001,  # 1 pip tolerance
    "retry_attempts": 3,
    "retry_delay_ms": 100,
    
    # Pre-calculated order parameters for speed
    "precalculate_orders": True,
    "order_cache_size": 100,
    "position_check_interval_ms": 500,
    
    # MT5 specific settings
    "mt5_magic_number": 54321,  # Unique magic number for current AI system
    "mt5_deviation": 10,
    "mt5_fill_policy": "FOK"  # Fill or Kill
}

# Dashboard and monitoring settings
DASHBOARD_SETTINGS = {
    "update_interval_seconds": 30,  # Update every 30 seconds (aligned with 3-min cycles)
    "port": 5000,
    "host": "localhost",
    "auto_refresh": True,
    "chart_history_minutes": 60,  # Show last hour on charts
    
    # Alert settings
    "enable_alerts": True,
    "alert_channels": ["dashboard", "console"],
    "critical_alert_threshold": 0.01,  # 1% drawdown alert
    
    # Performance tracking
    "track_execution_quality": True,
    "track_slippage": True,
    "track_latency": True,
    "performance_window_hours": 24
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": {
        "file": {
            "filename": "logs/synthetic_trading.log",
            "max_bytes": 10485760,  # 10MB
            "backup_count": 5
        },
        "console": {
            "enabled": True
        }
    },
    
    # Specific loggers
    "loggers": {
        "data_collector": "DEBUG",
        "pattern_detector": "INFO", 
        "trading_engine": "INFO",
        "risk_manager": "WARNING",
        "execution": "DEBUG"
    }
}

# Directory structure
DIRECTORIES = [
    "data",
    "data/synthetic_cache", 
    "data/historical",
    "data/realtime",
    "data/patterns",
    "data/models",
    "logs",
    "models/saved",
    "reports",
    "dashboard/static",
    "dashboard/templates",
    "tests",
    "backtest_results"
]
