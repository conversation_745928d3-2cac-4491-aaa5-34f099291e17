#!/usr/bin/env python3
"""
Test Dashboard Fix
Verify that the dashboard now shows account balance instead of confusing historical P&L
and that daily P&L is correctly calculated.
"""

import sys
import os
import logging
from datetime import datetime, timezone

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

def test_dashboard_metrics():
    """Test the dashboard metrics after our fixes."""
    print("🧪 TESTING DASHBOARD METRICS FIX")
    print("=" * 50)
    
    try:
        from dashboard_server import DashboardDataManager
        
        # Create dashboard manager
        print("📊 Creating dashboard manager...")
        dashboard_manager = DashboardDataManager()
        
        # Initialize if needed
        if hasattr(dashboard_manager, 'initialize_trading_system'):
            print("🔧 Initializing trading system...")
            dashboard_manager.initialize_trading_system()
        
        # Update trade metrics
        print("💰 Updating trade metrics...")
        dashboard_manager._update_trade_metrics()
        
        # Get dashboard data
        dashboard_data = dashboard_manager.get_dashboard_data()
        trade_metrics = dashboard_data.get("trade_metrics", {})
        
        # Display the metrics
        print(f"\n📊 DASHBOARD METRICS (AFTER FIX):")
        print(f"   Account Balance: ${trade_metrics.get('account_balance', 0.0):.2f}")
        print(f"   Daily P&L: ${trade_metrics.get('daily_pnl', 0.0):.2f}")
        print(f"   Total Trades: {trade_metrics.get('total_trades', 0)}")
        print(f"   Win Rate: {trade_metrics.get('win_rate', 0.0)*100:.1f}%")
        print(f"   Active Positions: {trade_metrics.get('active_positions', 0)}")
        
        # Check if we have the expected fields
        has_account_balance = 'account_balance' in trade_metrics
        has_daily_pnl = 'daily_pnl' in trade_metrics
        no_total_pnl = 'total_pnl' not in trade_metrics or trade_metrics.get('total_pnl') == trade_metrics.get('account_balance')
        
        print(f"\n✅ VERIFICATION:")
        print(f"   Has Account Balance: {'✅' if has_account_balance else '❌'}")
        print(f"   Has Daily P&L: {'✅' if has_daily_pnl else '❌'}")
        print(f"   No Confusing Total P&L: {'✅' if no_total_pnl else '❌'}")
        
        return has_account_balance and has_daily_pnl
        
    except Exception as e:
        print(f"❌ Dashboard metrics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_pnl_comparison():
    """Compare our fixed P&L calculation with what dashboard shows."""
    print("\n🧪 TESTING P&L CALCULATION COMPARISON")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        import config
        from order_execution_system import OrderExecutionSystem
        from synthetic_data_collector import SyntheticDataCollector
        
        # Initialize MT5
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return False
        
        print("✅ MT5 connected")
        
        # Create data collector and order executor
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        # Force update daily P&L
        order_executor._update_daily_pnl()
        
        # Get the calculated daily P&L
        calculated_daily_pnl = order_executor.daily_pnl
        
        # Get execution statistics
        exec_stats = order_executor.get_execution_statistics()
        stats_daily_pnl = exec_stats.get("daily_pnl", 0.0)
        
        print(f"📊 P&L COMPARISON:")
        print(f"   Direct calculation: ${calculated_daily_pnl:.2f}")
        print(f"   Execution stats: ${stats_daily_pnl:.2f}")
        print(f"   Match: {'✅' if abs(calculated_daily_pnl - stats_daily_pnl) < 0.01 else '❌'}")
        
        # Get account info
        account_info = mt5.account_info()
        if account_info:
            print(f"   Account Balance: ${account_info.balance:.2f}")
            print(f"   Account Equity: ${account_info.equity:.2f}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ P&L comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_changes():
    """Test that HTML template has been updated."""
    print("\n🧪 TESTING HTML TEMPLATE CHANGES")
    print("=" * 50)
    
    try:
        # Read the dashboard HTML template
        with open('dashboard/templates/dashboard.html', 'r') as f:
            html_content = f.read()
        
        # Check for changes
        has_account_balance = 'Account Balance' in html_content
        has_account_balance_id = 'id="account-balance"' in html_content
        no_total_pnl_label = 'Total P&L' not in html_content
        
        print(f"📄 HTML TEMPLATE VERIFICATION:")
        print(f"   Has 'Account Balance' label: {'✅' if has_account_balance else '❌'}")
        print(f"   Has account-balance ID: {'✅' if has_account_balance_id else '❌'}")
        print(f"   Removed 'Total P&L' label: {'✅' if no_total_pnl_label else '❌'}")
        
        return has_account_balance and has_account_balance_id and no_total_pnl_label
        
    except Exception as e:
        print(f"❌ HTML template test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 DASHBOARD FIX VERIFICATION")
    print("=" * 60)
    print(f"🕐 Test time: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S GMT')}")
    
    # Run tests
    tests = [
        ("Dashboard Metrics", test_dashboard_metrics),
        ("P&L Calculation", test_direct_pnl_comparison),
        ("HTML Template", test_html_changes),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 DASHBOARD FIXES WORKING!")
        print("\n📊 What's been fixed:")
        print("   ✅ Dashboard shows Account Balance instead of confusing Total P&L")
        print("   ✅ Daily P&L calculation fixed (from 00:00 GMT)")
        print("   ✅ No more historical P&L confusion")
        print("   ✅ Clear separation between account balance and daily performance")
        
        print("\n🎯 Expected dashboard display:")
        print("   • Account Balance: Your actual MT5 account balance")
        print("   • Daily P&L: Today's trading performance from 00:00 GMT")
        print("   • No more confusing -$18.81 historical P&L!")
    else:
        print("⚠️  Some fixes need attention")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
