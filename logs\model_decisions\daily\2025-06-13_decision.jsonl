{"timestamp": "2025-06-13T14:59:13.574661", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7761803269386292, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T14:59:13.637464", "model_name": "short_term_momentum_rf", "signal": -2, "confidence": 0.42326135205851884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T14:59:13.642486", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T14:59:13.857137", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.76406329870224, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T14:59:13.922372", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.9187824925031498, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T14:59:13.932505", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999411106109619, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T14:59:14.048198", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9988271594047546, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T14:59:14.111874", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T14:59:14.117027", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T15:45:14.280676", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8887560963630676, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T15:45:14.342331", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8112281617681287, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T15:45:14.561369", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6234304308891296, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T15:45:14.623663", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5090657265929118, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T15:45:14.633210", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999802112579346, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T15:45:14.748473", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.6604070663452148, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T15:45:14.811636", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9791848709611868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T15:45:14.817679", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T15:48:13.151299", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7110677361488342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T15:48:13.739661", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.6945168611576409, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T15:48:13.815498", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6031180024147034, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T15:48:13.914897", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5769158576439337, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T15:48:13.925056", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998041987419128, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T15:48:14.010157", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9986608028411865, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T15:48:14.088937", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9721428571428572, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T15:48:14.094051", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T15:51:12.619079", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7110677361488342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T15:51:13.026741", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.694516861157641, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T15:51:13.101123", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6031180024147034, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T15:51:13.166095", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5769158576439337, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T15:51:13.176334", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998041987419128, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T15:51:13.255638", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9986608028411865, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T15:51:13.348437", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9721428571428572, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T15:51:13.353542", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T15:54:12.412419", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7110677361488342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T15:54:12.662558", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.694516861157641, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T15:54:12.722692", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6031180024147034, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T15:54:12.786238", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5769158576439338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T15:54:12.815107", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998041987419128, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T15:54:12.889680", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9986608028411865, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T15:54:12.959262", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9721428571428572, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T15:54:12.983687", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T15:58:49.181745", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9136117696762085, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T15:58:49.246103", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.921263248888311, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T15:58:49.589314", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.30519792437553406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T15:58:49.630236", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8404547714937058, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T15:58:49.638838", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999558925628662, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T15:58:49.748180", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9979273080825806, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T15:58:49.801121", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T15:58:49.805570", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T17:53:34.715053", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9532220959663391, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T17:53:34.776215", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9496397312069482, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T17:53:34.782461", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T17:53:35.003597", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.3392105996608734, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T17:53:35.065864", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4042941404875159, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T17:53:35.076192", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999221563339233, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T17:53:35.188853", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9757202863693237, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T17:53:35.252209", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T17:53:35.257482", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T17:57:22.056209", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9884727597236633, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T17:57:22.160821", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9684130800794957, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T17:57:22.166279", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T17:57:22.383382", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9093660116195679, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T17:57:22.448760", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4147484799346398, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T17:57:22.458834", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.964496374130249, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T17:57:22.573647", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9997848868370056, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T17:57:22.634919", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9970833333333334, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T17:57:22.640404", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:00:20.175913", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9862196445465088, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:00:20.357616", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.982930194805195, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:00:20.376140", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:00:20.432625", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9093345999717712, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:00:20.492400", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.44529409909065876, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:00:20.533093", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9980837106704712, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:00:20.636463", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9984427094459534, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:00:20.696217", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9970833333333334, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:00:20.718752", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:03:20.544509", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9864643812179565, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:03:20.616764", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9792169913419912, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:03:20.622915", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:03:20.695262", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9102814793586731, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:03:20.760912", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.32057916565655054, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:03:20.771460", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999243021011353, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:03:20.883135", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9743868112564087, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:03:20.952123", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:03:20.958396", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:06:20.379639", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9827917218208313, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:06:20.677038", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9928571428571429, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:06:20.682568", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:06:20.746898", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8996872901916504, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:06:20.808908", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3903620228939462, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:06:20.823410", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5970980525016785, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:06:20.897470", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9986116886138916, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:06:20.962197", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9993213866039953, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:06:20.982782", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:09:20.462892", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9479691982269287, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:09:20.776638", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9716666666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:09:20.782062", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:09:20.849268", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8731071949005127, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:09:20.913767", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3676508150569581, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:09:20.923622", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9979619979858398, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:09:21.002694", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9980779886245728, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:09:21.087866", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9993213866039953, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:09:21.093211", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:12:20.227228", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8429914712905884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:12:20.715236", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7720509402760061, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:12:20.720755", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:12:20.775861", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8834813833236694, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:12:20.837339", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.39971564053901154, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:12:20.861294", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9979054927825928, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:12:20.936893", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9783958196640015, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:12:21.003520", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9936984126984126, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:12:21.022881", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:15:20.223421", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9898248314857483, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:15:20.408597", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9964285714285714, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:15:20.413638", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:15:20.470687", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5579705834388733, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:15:20.533158", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6449240602821598, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:15:20.543246", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999330043792725, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:15:20.619655", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.98831707239151, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:15:20.722809", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.895, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:15:20.727228", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:18:20.288262", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5763164162635803, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:18:20.351279", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:18:20.357543", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:18:20.415636", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.854570209980011, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:18:20.479652", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6201399951793588, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:18:20.487679", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999496936798096, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:18:20.560471", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9850173592567444, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:18:20.643433", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9985714285714287, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:18:20.648458", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:21:20.187302", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5562496781349182, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:21:20.352938", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9883590173196675, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:21:20.701875", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:21:20.771986", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9039256572723389, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:21:20.834051", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6486268665818606, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:21:20.872681", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999419450759888, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:21:20.973493", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.8227603435516357, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:21:21.034052", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9197171964680385, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:21:21.059651", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:24:20.258077", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5673606991767883, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:24:20.317715", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9883590173196675, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:24:20.323571", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:24:20.378673", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9067236185073853, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:24:20.448865", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6619325812308827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:24:20.457899", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999535083770752, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:24:20.532975", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.8090183138847351, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:24:20.602731", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9197171964680385, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:24:20.607759", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:27:20.146018", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9985089898109436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:27:20.458183", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9851201044386422, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:27:20.463074", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:27:20.517453", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.896777868270874, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:27:20.578058", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5812525197803352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:27:20.598344", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997755885124207, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:27:20.707356", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999783039093018, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:27:20.766673", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:27:20.772660", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:30:20.307191", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9988992214202881, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:30:20.369125", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9840416666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:30:20.560169", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:30:20.647435", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8944592475891113, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:30:20.710193", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6813585368853665, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:30:20.719203", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9994555115699768, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:30:20.794775", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999281108379364, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:30:20.860452", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9966666666666667, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:30:20.892810", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:33:20.245990", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9786185026168823, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:33:20.309578", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:33:20.314889", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:33:20.371468", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8671693801879883, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:33:20.434298", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.9609799097443116, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:33:20.444733", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999572038650513, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:33:20.519307", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9886776804924011, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:33:20.604330", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:33:20.609488", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:36:20.207268", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.914268970489502, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:36:21.002088", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9615523906899718, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:36:21.007154", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:36:21.064819", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8849276304244995, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:36:21.127783", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8492767362846194, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:36:21.136846", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999585151672363, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:36:21.211009", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9914617538452148, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:36:21.276260", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:36:21.281501", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:39:20.184990", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9851186871528625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:39:21.064414", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9956201044386424, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:39:21.069404", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:39:21.124187", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9020816683769226, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:39:21.183447", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7835331776762, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:39:21.199714", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999712705612183, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:39:21.271221", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9991725087165833, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:39:21.335650", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999419155691619, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:39:21.355975", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:42:20.212559", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9991338849067688, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:42:20.298470", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9912312155497532, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:42:20.303509", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:42:20.359473", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8989146947860718, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:42:20.419942", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.733995334609338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:42:20.429015", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9978862404823303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:42:20.504602", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992594122886658, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:42:20.569749", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9966666666666667, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:42:20.583873", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:45:20.240574", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975094795227051, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:45:20.315849", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:45:20.320873", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:45:20.375347", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9002411365509033, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:45:20.461869", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8930766288823453, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:45:20.470140", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999574422836304, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:45:20.541308", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992098808288574, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:45:20.628853", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:45:20.633567", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:48:20.232530", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9534359574317932, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:48:20.295922", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:48:20.300944", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:48:20.357910", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8957357406616211, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:48:20.419363", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8263121071717258, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:48:20.428395", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999494552612305, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:48:20.503580", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9856396913528442, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:48:20.567472", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:48:20.572519", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:51:20.176625", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6613670587539673, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:51:20.306472", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9681204637954366, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:51:20.311569", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:51:20.366532", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9060928821563721, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:51:20.425574", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5127967954387944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:51:20.456556", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999418258666992, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:51:20.528774", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9996170997619629, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:51:20.594859", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:51:20.617001", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:54:20.237719", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5351136326789856, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:54:20.315459", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992851188022763, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:54:20.321777", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:54:20.378953", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9108179807662964, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:54:20.440939", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6648529690578809, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:54:20.450492", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999500513076782, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:54:20.526062", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998401403427124, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:54:20.591121", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:54:20.596270", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T18:57:20.163698", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9977672100067139, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T18:57:20.477057", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9997851188022763, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T18:57:20.482081", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T18:57:20.538737", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9041712880134583, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T18:57:20.600048", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6735863579627547, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T18:57:20.609115", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999551773071289, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T18:57:20.684852", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T18:57:20.788804", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T18:57:20.793591", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:00:20.260917", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9977580904960632, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:00:20.324343", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.994779285805462, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:00:20.329410", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:00:20.387878", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.904076337814331, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:00:20.447090", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5343343153338189, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:00:20.456345", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999436140060425, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:00:20.531522", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:00:20.595390", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:00:20.600430", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:03:20.174711", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5840521454811096, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:03:20.635775", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9968200280112045, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:03:20.640920", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:03:20.695012", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9014595746994019, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:03:20.755232", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8671067194342933, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:03:20.782391", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998965263366699, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:03:20.854423", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999212384223938, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:03:20.916722", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:03:20.943483", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:06:20.225946", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9300137758255005, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:06:20.287244", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9880895201866244, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:06:20.613864", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:06:20.686589", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9134771823883057, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:06:20.746838", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6844202894358421, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:06:20.755892", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999552965164185, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:06:20.831472", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9974424839019775, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:06:20.893860", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:06:20.926649", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:09:20.449425", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6563397645950317, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:09:20.603324", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9907291139323946, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:09:20.608193", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:09:20.660835", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9077851176261902, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:09:20.720581", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7338364185318745, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:09:20.749963", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999189376831055, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:09:20.820733", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992684721946716, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:09:20.886012", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999150913775522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:09:20.911020", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:12:20.207161", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9983950257301331, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:12:21.682245", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9888134920634921, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:12:21.687434", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:12:21.742963", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8941940665245056, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:12:21.804809", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.692172507545761, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:12:21.813908", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999444484710693, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:12:21.892485", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999929666519165, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:12:21.958307", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:12:21.962904", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:15:20.321793", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9983968138694763, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:15:20.387054", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9836282249742002, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:15:20.392578", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:15:20.447084", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.892329752445221, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:15:20.509158", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6839510623243159, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:15:20.518210", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999477863311768, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:15:20.593565", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999345541000366, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:15:20.671307", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:15:20.677095", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:18:20.296594", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9292038679122925, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:18:20.359943", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:18:20.364968", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:18:20.422060", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9050410985946655, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:18:20.482980", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7377535316128094, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:18:20.492038", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998364448547363, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:18:20.568698", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9990311861038208, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:18:20.634182", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:18:20.639256", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:21:20.199412", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9988439083099365, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:21:20.616646", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9909913493813268, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:21:20.622207", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:21:20.676464", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8897590041160583, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:21:20.736752", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8627129073253778, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:21:20.763244", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.6593042612075806, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:21:20.834821", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.997680127620697, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:21:20.902113", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:21:20.922647", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:24:20.197906", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9987181425094604, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:24:20.813506", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9880330160479935, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:24:20.818801", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:24:20.875928", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8767673373222351, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:24:20.937005", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8785956589577825, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:24:20.946079", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9994105100631714, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:24:21.020651", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998688697814941, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:24:21.089813", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9918486738129595, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:24:21.094848", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:27:20.253902", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9984350800514221, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:27:20.318501", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9993386683738796, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:27:20.323540", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:27:20.381220", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8856156468391418, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:27:20.442025", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9235417040072257, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:27:20.451246", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997432827949524, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:27:20.549754", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999780654907227, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:27:20.610226", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:27:20.616244", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:30:20.311854", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9984318614006042, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:30:20.374396", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9916500619880902, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:30:20.379415", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:30:20.436426", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8852021098136902, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:30:20.498198", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9235417040072257, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:30:20.507294", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999433696269989, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:30:20.583627", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999790191650391, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:30:20.650534", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:30:20.655652", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:33:20.230092", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.49091750383377075, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:33:20.488948", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9966666666666666, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:33:20.835640", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:33:20.908618", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8607637286186218, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:33:20.968898", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6630808056428772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:33:20.978017", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999316930770874, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:33:21.054591", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.8534646034240723, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:33:21.121104", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.895, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:33:21.147497", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:36:20.320957", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9618605375289917, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:36:20.385718", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7743710188495107, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:36:20.390783", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:36:20.446612", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5421113967895508, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:36:20.506939", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7886788980192547, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:36:20.515996", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999572038650513, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:36:20.592073", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992091059684753, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:36:20.656840", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9997992030185546, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:36:20.661902", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:39:20.172264", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9565234184265137, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:39:20.612790", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:39:20.617810", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:39:20.673995", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8508837223052979, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:39:20.734613", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7436938516760254, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:39:20.744167", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999527931213379, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:39:20.817966", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999576807022095, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:39:21.022809", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:39:21.043503", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:42:20.281101", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9568185210227966, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:42:20.357614", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:42:20.362648", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:42:20.419819", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8612115383148193, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:42:20.480770", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.8157510685947428, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:42:20.489836", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999964714050293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:42:20.563916", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:42:20.632044", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:42:20.637076", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:45:20.185141", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9588308930397034, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:45:20.850860", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:45:20.855954", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:45:20.912110", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8812434077262878, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:45:20.973278", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7786467924566937, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:45:20.982406", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998800754547119, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:45:21.057218", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:45:21.144435", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:45:21.149476", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:48:20.224829", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6347935795783997, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:48:20.322471", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9985416666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:48:20.327558", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:48:20.382077", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.33481937646865845, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:48:20.444370", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4441582646835556, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:48:20.453462", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998555183410645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:48:20.529120", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9916802048683167, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:48:20.594010", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:48:20.599077", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:51:20.191139", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8810657858848572, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:51:20.481771", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9631228361300476, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:51:20.486766", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:51:20.540184", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9158303141593933, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:51:20.599170", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6657376009104337, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:51:20.618248", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999338388442993, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:51:20.689118", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992218017578125, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:51:20.752771", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:51:20.774277", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:54:20.281634", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7744879722595215, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:54:20.426018", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9993913955990621, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:54:20.431039", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:54:20.487603", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9119418263435364, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:54:20.551084", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6825979024777616, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:54:20.561107", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999669790267944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:54:20.635331", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998422861099243, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:54:20.727651", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:54:20.732215", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T19:57:20.174609", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9981822967529297, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T19:57:20.246637", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994105282521234, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T19:57:20.251700", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T19:57:20.307680", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9029483795166016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T19:57:20.370070", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7579333334549833, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T19:57:20.380171", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999469518661499, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T19:57:20.452467", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T19:57:20.531657", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T19:57:20.536684", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:00:20.228545", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9982172846794128, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:00:20.289338", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9934019627867162, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:00:20.295383", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:00:20.348937", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9020339250564575, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:00:20.422159", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7584308433079089, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:00:20.431303", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999501705169678, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:00:20.503375", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:00:20.591472", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:00:20.595900", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:03:20.189882", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.704598605632782, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:03:20.251328", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9987843137254901, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:03:21.147162", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:03:21.217123", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8787489533424377, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:03:21.280000", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5948475212985485, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:03:21.289314", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999703168869019, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:03:21.363781", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9901404976844788, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:03:21.431035", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:03:21.436095", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:06:20.246701", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7721329927444458, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:06:20.308249", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9657290639738272, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:06:20.313291", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:06:20.367992", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9105556607246399, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:06:20.453008", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.929724412135932, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:06:20.463147", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999579191207886, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:06:20.535626", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992606043815613, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:06:20.622496", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:06:20.627532", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:09:20.199991", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5384352803230286, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:09:20.260886", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999711774244582, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:09:20.266430", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:09:20.320979", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9106806516647339, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:09:20.400022", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8772965667580004, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:09:20.409880", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999549388885498, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:09:20.481449", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999386072158813, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:09:20.570446", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:09:20.575473", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:12:20.226672", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978452920913696, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:12:20.289236", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9923506504407902, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:12:20.294326", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:12:20.350871", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9042565226554871, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:12:20.422111", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8964349766786013, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:12:20.430216", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999463558197021, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:12:20.501567", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:12:20.581879", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:12:20.585898", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:15:20.238688", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978737831115723, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:15:20.302055", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9966056379719623, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:15:20.307127", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:15:20.364080", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9044607281684875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:15:20.424983", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7862186997737988, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:15:20.434138", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999463558197021, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:15:20.506692", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:15:20.596866", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:15:20.601876", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:18:20.238865", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.925469160079956, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:18:20.879588", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.952186827061827, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:18:20.884706", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:18:20.944628", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8725275993347168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:18:21.009216", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4785658397248481, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:18:21.019339", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998782873153687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:18:21.097483", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992421865463257, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:18:21.191816", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:18:21.197179", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:21:20.209658", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9733388423919678, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:21:20.291336", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8665518468058434, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:21:20.296383", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:21:20.353448", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7319523096084595, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:21:20.415874", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5432494708598888, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:21:20.425230", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999359846115112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:21:20.498898", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9987509250640869, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:21:20.583958", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9147171964680385, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:21:20.589096", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:24:20.208358", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7578824162483215, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:24:20.402141", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9982699784140197, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:24:20.407169", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:24:20.462046", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8713629245758057, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:24:20.521857", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5480704870901983, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:24:20.542496", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999070167541504, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:24:20.614049", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998624324798584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:24:20.677761", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:24:20.699369", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:27:20.600359", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5214336514472961, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:27:20.678048", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:27:20.684572", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:27:20.745482", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8777026534080505, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:27:20.807598", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.40063971706223783, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:27:20.816667", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999412298202515, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:27:20.895818", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:27:20.963841", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:27:20.968941", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:30:20.444238", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5141807198524475, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:30:20.509694", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:30:20.515894", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:30:20.582060", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8743891716003418, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:30:20.649785", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3946868393313227, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:30:20.659192", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998637437820435, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:30:20.746794", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999765157699585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:30:20.811209", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:30:20.817730", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:33:20.199291", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8470116853713989, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:33:20.433620", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:33:20.439522", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:33:20.493043", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8814224004745483, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:33:20.551587", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6775058328806535, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:33:20.576279", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999065399169922, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:33:20.647190", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9991171956062317, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:33:20.713899", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:33:20.738265", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:36:20.244503", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9391279220581055, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:36:20.570362", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8487807559100156, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:36:20.575383", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:36:20.631424", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8364487886428833, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:36:20.691632", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7349002402551713, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:36:20.700677", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999569654464722, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:36:20.775449", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9987688660621643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:36:20.837782", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9146514069943542, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:36:20.843304", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:39:20.214977", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8912609815597534, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:39:20.288621", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:39:20.293682", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:39:20.350663", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8526465892791748, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:39:20.411898", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6520111369859349, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:39:20.420939", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999144077301025, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:39:20.493406", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999840259552002, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:39:20.573175", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:39:20.578209", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:42:20.216490", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9728297591209412, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:42:20.284319", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:42:20.289343", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:42:20.346111", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8541245460510254, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:42:20.407739", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6756021610778058, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:42:20.416801", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999063014984131, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:42:20.491374", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:42:20.558924", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:42:20.563929", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:45:20.268115", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9726316928863525, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:45:20.332634", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:45:20.337549", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:45:20.392178", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8778896927833557, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:45:20.468966", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6661017518764349, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:45:20.477719", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999951958656311, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:45:20.548350", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:45:20.637929", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:45:20.641974", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:48:20.202843", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9985690116882324, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:48:20.355959", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.995, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:48:20.360984", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:48:20.415315", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.852389931678772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:48:20.475488", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6034525803689553, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:48:20.484553", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998884201049805, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:48:20.559012", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9958111047744751, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:48:20.623864", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:48:20.629354", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:51:20.253877", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9991829991340637, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:51:20.330092", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9837669612522235, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:51:20.335132", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:51:20.391186", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8666574358940125, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:51:20.455176", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4954035097961284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:51:20.464218", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9774331450462341, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:51:20.537285", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9991773962974548, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:51:20.614486", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:51:20.618502", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:54:20.263565", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9992021918296814, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:54:20.326254", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999973835688121, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:54:20.331348", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:54:20.386463", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.872665524482727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:54:20.469760", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.45845452868425424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:54:20.477810", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9441744685173035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:54:20.552272", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999309778213501, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:54:20.637909", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:54:20.642698", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T20:57:20.216784", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9991990923881531, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T20:57:20.378051", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999973835688121, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T20:57:20.383879", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T20:57:20.436340", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8669137954711914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T20:57:20.496435", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4614321439814992, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T20:57:20.521165", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9794603586196899, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T20:57:20.594589", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999099969863892, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T20:57:20.662160", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T20:57:20.682100", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:00:20.212932", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9991984963417053, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:00:20.297503", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999973835688121, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:00:20.302526", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:00:20.357646", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8669193983078003, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:00:20.419450", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4455576318546504, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:00:20.429067", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9698454141616821, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:00:20.504711", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999884843826294, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:00:20.568733", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:00:20.574999", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:03:20.234600", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6376051306724548, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:03:20.741208", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9945114942528737, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:03:20.746252", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:03:20.801753", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8930829763412476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:03:20.863076", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.8242792792075763, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:03:20.889560", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999518394470215, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:03:20.962213", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9958397150039673, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:03:21.028632", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:03:21.050065", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:06:20.198722", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.593031108379364, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:06:20.262579", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.930786567845188, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:06:20.266596", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:06:20.322046", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8560726642608643, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:06:20.400359", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7001232023697385, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:06:20.409615", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999140501022339, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:06:20.480517", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9990088939666748, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:06:20.568705", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.999766735486087, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:06:20.572728", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:09:20.206735", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7147746086120605, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:09:20.267906", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:09:20.273440", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:09:20.328216", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8666107058525085, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:09:20.401721", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6262318345272055, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:09:20.411034", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999494552612305, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:09:20.482110", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999825954437256, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:09:20.572896", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:09:20.578717", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:12:20.205345", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9317280054092407, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:12:20.267741", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:12:20.272767", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:12:20.329254", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8702367544174194, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:12:20.390161", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49840928210375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:12:20.398747", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998840093612671, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:12:20.473820", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:12:20.540804", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:12:20.545826", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:15:20.221078", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9323065280914307, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:15:20.285414", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:15:20.290442", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:15:20.344998", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8802818655967712, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:15:20.432749", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.510657260566247, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:15:20.441866", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998098015785217, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:15:20.514908", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:15:20.602820", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:15:20.607947", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:18:20.205649", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.48665931820869446, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:18:20.293295", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9941666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:18:20.298342", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:18:20.356397", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.856130063533783, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:18:20.418857", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7009876052785502, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:18:20.427960", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999944806098938, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:18:20.502532", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9989842772483826, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:18:20.568217", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:18:20.573255", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:21:20.240787", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7249990105628967, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:21:20.986765", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9853521555998889, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:21:20.992341", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:21:21.046837", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9127880334854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:21:21.108046", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8949702527842885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:21:21.117211", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999537467956543, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:21:21.192506", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992578625679016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:21:21.281901", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:21:21.287834", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:24:20.244233", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.550894021987915, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:24:20.326059", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:24:20.331089", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:24:20.386579", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9091668725013733, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:24:20.486293", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8766849111483546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:24:20.496423", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999454021453857, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:24:20.570499", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.99997878074646, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:24:20.634939", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:24:20.639981", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:27:20.206871", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978249073028564, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:27:20.269203", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:27:20.274271", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:27:20.328965", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9040236473083496, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:27:20.409264", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8234677247227605, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:27:20.418336", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999511241912842, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:27:20.489405", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:27:20.577733", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:27:20.582276", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:30:20.245074", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9239042401313782, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:30:20.328833", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7901302170052174, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:30:20.334853", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:30:20.391742", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.821887195110321, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:30:20.453182", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.3605235672549844, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:30:20.462273", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999239444732666, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:30:20.538806", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9895022511482239, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:30:20.605066", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:30:20.611257", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:33:20.287555", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9465353488922119, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:33:20.720716", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9917857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:33:20.724801", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:33:20.778506", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7118890881538391, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:33:20.837987", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.40559030083291114, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:33:20.867363", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998903274536133, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:33:20.940802", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9921350479125977, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:33:21.003646", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:33:21.028854", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:36:20.213524", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5233014225959778, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:36:20.290175", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9987144228584641, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:36:20.316456", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:36:20.372541", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8727802038192749, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:36:20.433494", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49792763393149314, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:36:20.442558", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999009370803833, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:36:20.516138", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998629093170166, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:36:20.580295", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:36:20.585360", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:39:20.242010", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5203325152397156, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:39:20.316960", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992699784140197, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:39:20.321996", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:39:20.378014", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.875421941280365, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:39:20.438392", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5209806435377762, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:39:20.447662", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999228715896606, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:39:20.523607", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998527765274048, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:39:20.592018", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:39:20.604534", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:42:20.276547", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9022632241249084, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:42:20.339862", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:42:20.371228", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:42:20.427674", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8850135207176208, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:42:20.485973", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4598281728600685, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:42:20.528367", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999610185623169, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:42:20.629619", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999780654907227, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:42:20.688288", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:42:20.725701", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:45:20.275446", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8984980583190918, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:45:20.342053", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:45:20.347107", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:45:20.406110", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8833634257316589, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:45:20.469166", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.453713884629568, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:45:20.478248", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999595880508423, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:45:20.550879", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:45:20.616874", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:45:20.622917", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:48:20.326029", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8461982607841492, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:48:20.479754", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9916964285714285, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:48:20.484922", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:48:20.540400", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7005819082260132, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:48:20.600666", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5051637578183553, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:48:20.609675", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999606609344482, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:48:20.683246", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9903632998466492, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:48:20.757327", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:48:20.764354", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:51:20.319700", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7276498079299927, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:51:20.966555", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.98162309156048, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:51:20.972072", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:51:21.033101", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9152041077613831, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:51:21.096006", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6354112729614796, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:51:21.104317", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999511241912842, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:51:21.181466", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992546439170837, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:51:21.279330", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:51:21.283851", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:54:20.335401", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.49634677171707153, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:54:20.655386", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:54:20.660944", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:54:20.715566", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9110392332077026, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:54:20.776593", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7187388046872739, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:54:20.802122", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999955415725708, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:54:20.874187", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999843835830688, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:54:20.939056", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:54:20.958076", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T21:57:20.261188", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978098273277283, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T21:57:20.334182", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T21:57:20.663651", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T21:57:20.734039", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9042593836784363, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T21:57:20.795162", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7122222905477322, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T21:57:20.805190", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999595880508423, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T21:57:20.880183", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T21:57:20.945843", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T21:57:20.951716", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:00:20.216192", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9979497790336609, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:00:20.279052", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999651314752441, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:00:20.283784", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:00:20.338708", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9044923186302185, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:00:20.429303", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.661653930464941, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:00:20.437690", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999595880508423, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:00:20.513649", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:00:20.596915", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:00:20.601948", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:03:20.519277", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7133336067199707, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:03:20.602829", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9873809523809524, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:03:20.607863", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:03:20.664392", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7060644626617432, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:03:20.725035", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4480464991652619, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:03:20.734313", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998468160629272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:03:20.805367", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9926221370697021, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:03:20.921484", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:03:20.926738", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:06:20.261310", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9880372285842896, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:06:20.335081", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8718746603626768, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:06:20.340893", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:06:20.394886", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7328270077705383, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:06:20.479572", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5497394013994603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:06:20.488797", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999524354934692, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:06:20.560049", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9987521171569824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:06:20.648801", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9147171964680385, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:06:20.652833", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:09:20.217695", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5213349461555481, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:09:20.288342", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992699784140197, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:09:20.293425", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:09:20.350472", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8711023926734924, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:09:20.413015", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4962236127245573, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:09:20.422103", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999090433120728, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:09:20.496674", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998626708984375, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:09:20.560337", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:09:20.566405", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:12:20.250358", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8829235434532166, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:12:20.313251", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:12:20.318168", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:12:20.373038", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.877493143081665, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:12:20.445324", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4245525005652272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:12:20.454387", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999396800994873, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:12:20.528467", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:12:20.612582", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:12:20.617653", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:15:20.304870", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8856889605522156, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:15:20.384412", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:15:20.389581", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:15:20.446612", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8833197951316833, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:15:20.509872", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4050282528954609, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:15:20.519118", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999937891960144, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:15:20.589196", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:15:20.675797", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:15:20.680816", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:18:20.196476", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9604110717773438, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:18:20.259308", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9945114942528737, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:18:20.318613", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:18:20.387747", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8384067416191101, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:18:20.448409", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4465668414050238, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:18:20.489971", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998546838760376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:18:20.591987", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9910805821418762, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:18:20.653186", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:18:20.676387", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:21:20.253571", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4137064814567566, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:21:20.358846", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8751471420582735, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:21:20.364053", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:21:20.421121", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8631323575973511, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:21:20.483100", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6881200868878063, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:21:20.492222", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999962329864502, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:21:20.566290", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9994909763336182, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:21:20.658019", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9998436585630102, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:21:20.662241", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:24:20.218591", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7168133854866028, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:24:20.594756", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:24:20.599846", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:24:20.655073", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8670464754104614, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:24:20.714752", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5187956930845838, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:24:20.731940", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999922513961792, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:24:20.804554", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999842643737793, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:24:20.867868", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:24:20.898436", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:27:20.286656", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.93300461769104, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:27:20.810416", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:27:20.815500", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:27:20.872100", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8699474930763245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:27:20.935735", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5557383599829204, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:27:20.944853", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998798370361328, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:27:21.017729", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:27:21.083265", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:27:21.088301", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:30:20.285645", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9913374185562134, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:30:20.366715", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9985714285714287, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:30:20.681531", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:30:20.749970", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.27499035000801086, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:30:20.810697", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.372549349706788, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:30:20.852736", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999312162399292, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:30:20.944789", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9930288195610046, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:30:21.007590", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.895, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:30:21.039950", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:33:20.271706", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8941766023635864, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:33:20.488611", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9955416666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:33:20.838551", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:33:20.910721", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.874101996421814, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:33:20.970806", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49015798869618643, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:33:20.979974", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999061822891235, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:33:21.055101", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9912883043289185, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:33:21.118760", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:33:21.123784", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:36:20.277347", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.44956621527671814, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:36:20.339935", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9993913955990621, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:36:20.344998", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:36:20.401839", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9083704948425293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:36:20.463801", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.45142218126346456, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:36:20.511328", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999492168426514, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:36:20.613484", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999976634979248, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:36:20.675195", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:36:20.709096", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:39:20.228365", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.997923731803894, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:39:20.661065", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:39:20.665083", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:39:20.718934", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9103079438209534, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:39:20.780767", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.47452366487617104, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:39:20.808253", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99996018409729, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:39:20.879013", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999663829803467, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:39:20.945040", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:39:20.965125", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:42:20.246684", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978031516075134, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:42:20.310917", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999352552481848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:42:20.315933", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:42:20.373099", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.903344988822937, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:42:20.433642", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.43270897508214284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:42:20.443068", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998892545700073, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:42:20.546894", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:42:20.608101", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:42:20.635331", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:45:20.208306", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9443474411964417, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:45:20.740064", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7831084441107727, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:45:20.745271", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:45:20.802192", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8451390266418457, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:45:20.864009", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.434431353190225, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:45:20.874558", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999228715896606, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:45:20.947129", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9819542169570923, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:45:21.023653", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.875, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:45:21.028688", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:48:20.262011", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9598712921142578, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:48:20.339604", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:48:20.344640", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:48:20.400967", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7098364233970642, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:48:20.463704", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5812733905316521, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:48:20.472854", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998250603675842, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:48:20.546471", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9926753640174866, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:48:20.627545", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:48:20.631561", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:51:20.228466", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7213946580886841, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:51:20.316393", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:51:20.321485", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:51:20.378135", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8599981665611267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:51:20.439186", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6984010288084168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:51:20.448287", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999359846115112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:51:20.523862", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998897314071655, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:51:20.587701", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:51:20.592707", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:54:20.269161", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7217726707458496, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:54:20.333391", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:54:20.339458", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:54:20.394407", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8661774396896362, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:54:20.458852", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7116337740802413, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:54:20.467969", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999359846115112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:54:20.539787", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998806715011597, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:54:20.627736", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:54:20.631751", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T22:57:20.209409", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9228367209434509, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T22:57:20.868938", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999842641283054, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T22:57:20.874022", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T22:57:20.929127", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8709316253662109, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T22:57:20.992028", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.732806670991737, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T22:57:21.001118", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999964714050293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T22:57:21.072695", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T22:57:21.163813", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T22:57:21.168836", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:00:20.239676", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9236255288124084, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:00:20.309801", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994287085727498, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:00:20.315009", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:00:20.370827", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8807032704353333, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:00:20.431227", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7612130987777782, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:00:20.440856", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999964714050293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:00:20.515935", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:00:20.594428", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:00:20.599434", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:03:20.211625", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8036537766456604, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:03:20.378594", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:03:20.721085", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:03:20.790555", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8644934892654419, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:03:20.849898", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.7441306638369997, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:03:20.892705", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998859167098999, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:03:20.995629", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999041736125946, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:03:21.054989", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:03:21.079392", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:06:20.288086", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9983287453651428, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:06:20.355338", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9996707576952728, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:06:20.360395", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:06:20.416396", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9051948189735413, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:06:20.478735", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4206114743703159, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:06:20.487813", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9995695948600769, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:06:20.562718", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999241828918457, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:06:20.662158", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:06:20.667677", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:09:20.207048", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9965448975563049, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:09:20.436862", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999701237729407, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:09:20.440882", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:09:20.498153", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.905888020992279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:09:20.558899", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3702694212210754, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:09:20.567956", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997833371162415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:09:20.641969", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9965873956680298, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:09:20.731677", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:09:20.736700", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:12:20.277185", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9986566305160522, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:12:20.398714", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999701237729407, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:12:20.403775", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:12:20.460286", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8852464556694031, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:12:20.523213", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.37962805855720766, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:12:20.532294", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997027516365051, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:12:20.607021", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999788999557495, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:12:20.672592", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:12:20.677612", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:15:20.277957", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9986599683761597, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:15:20.341228", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:15:20.346296", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:15:20.399862", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8848270177841187, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:15:20.475101", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3803572429242425, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:15:20.484162", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9994687438011169, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:15:20.555734", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999778270721436, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:15:20.630114", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:15:20.635171", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:18:20.276969", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5850418210029602, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:18:20.477920", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9966666666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:18:20.483172", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:18:20.539083", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8647298216819763, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:18:20.602140", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7343029000120846, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:18:20.611282", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999631643295288, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:18:20.683560", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9858686923980713, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:18:20.775476", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:18:20.780356", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:21:20.273788", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6999210715293884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:21:21.093672", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9758137652856166, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:21:21.098904", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:21:21.151372", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9016795754432678, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:21:21.214117", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.53512435250828, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:21:21.241740", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999641180038452, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:21:21.312838", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9885125160217285, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:21:21.379207", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.8999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:21:21.397217", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:24:20.301884", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.47823041677474976, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:24:21.088801", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9911367950974451, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:24:21.093909", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:24:21.150063", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.908787727355957, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:24:21.211676", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7066269599322417, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:24:21.220799", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999442100524902, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:24:21.295225", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:24:21.360345", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:24:21.365364", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:27:20.293394", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9977186918258667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:27:20.366112", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9911367950974451, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:27:20.372249", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:27:20.426543", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9039902091026306, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:27:20.533199", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6517651490868519, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:27:20.542729", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999395608901978, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:27:20.612496", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:27:20.700838", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:27:20.705883", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:30:20.309049", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9977608919143677, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:30:20.415528", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9966056379719623, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:30:20.419552", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:30:20.476544", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9050348401069641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:30:20.537897", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.36989932760890404, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:30:20.548238", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999516010284424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:30:20.627950", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:30:20.694844", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:30:20.699877", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:33:20.228960", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9403702020645142, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:33:20.304092", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:33:20.308477", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:33:20.363344", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8877681493759155, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:33:20.423378", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4192123340304893, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:33:20.447790", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999083280563354, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:33:20.517383", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9915468096733093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:33:20.580781", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:33:20.604571", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:36:20.268425", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.673616349697113, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:36:20.346495", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9696753921046752, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:36:20.352046", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:36:20.408940", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9085226655006409, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:36:20.471457", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6274813072896006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:36:20.480524", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999549388885498, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:36:20.555043", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9992111921310425, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:36:20.621689", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:36:20.626702", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:39:20.228714", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8255722522735596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:39:20.302659", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999711774244582, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:39:20.307784", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:39:20.362754", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.910642147064209, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:39:20.447773", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6296660320238141, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:39:20.456892", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999382495880127, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:39:20.529870", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999870777130127, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:39:20.616379", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999094480366943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:39:20.621574", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:42:20.287053", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9980010390281677, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:42:20.872155", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9999711774244582, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:42:20.876221", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:42:20.931433", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9042487740516663, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:42:20.994455", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6586372845110361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:42:21.004063", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999958872795105, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:42:21.078067", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999796152114868, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:42:21.142453", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9999943566591422, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:42:21.147481", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:45:20.224164", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9369505047798157, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:45:20.286474", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9837542735042736, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:45:20.576260", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:45:20.646407", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.905686616897583, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:45:20.708688", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.466292602886908, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:45:20.718976", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998499155044556, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:45:20.793548", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9988340735435486, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:45:20.856632", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:45:20.877063", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:48:20.270208", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9360150694847107, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:48:20.338883", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:48:20.343973", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:48:20.399919", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9108957052230835, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:48:20.461868", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4579342751799819, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:48:20.470962", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997484087944031, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:48:20.545644", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9982324242591858, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:48:20.609910", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:48:20.625276", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:51:20.226806", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.987700343132019, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:51:20.333120", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9800140086805729, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:51:20.660123", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:51:20.730208", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8729439973831177, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:51:20.792194", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5290461747484875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:51:20.832995", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9526543021202087, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:51:20.931868", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999126195907593, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:51:20.996474", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9977415309558165, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:51:21.019022", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:54:21.735495", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9872368574142456, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:54:21.799820", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9750140086805729, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:54:21.805104", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:54:21.862310", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8714689612388611, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:54:21.924027", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5585477818328473, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:54:21.933282", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8888017535209656, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:54:22.008041", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998843669891357, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:54:22.073006", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.9977415309558165, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:54:22.078044", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
{"timestamp": "2025-06-13T23:57:21.665988", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9870269894599915, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Pattern recognition for scalping"}
{"timestamp": "2025-06-13T23:57:21.759504", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9862692472363433, "timeframe_category": "short_term", "weight": 1.0, "features_used": 13, "model_purpose": "Momentum detection for quick trades"}
{"timestamp": "2025-06-13T23:57:22.257264", "model_name": "short_term_reversion_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 9, "model_purpose": "Mean reversion scalping"}
{"timestamp": "2025-06-13T23:57:22.327607", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8650768399238586, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 13, "model_purpose": "Trend continuation detection"}
{"timestamp": "2025-06-13T23:57:22.390395", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6527503043741723, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 11, "model_purpose": "Breakout detection"}
{"timestamp": "2025-06-13T23:57:22.399579", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5724589228630066, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 10, "model_purpose": "Volatility trading"}
{"timestamp": "2025-06-13T23:57:22.471650", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998902082443237, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Major trend analysis"}
{"timestamp": "2025-06-13T23:57:22.534895", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "long_term", "weight": 0.1, "features_used": 6, "model_purpose": "Support/resistance trading"}
{"timestamp": "2025-06-13T23:57:22.558674", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524172512, "timeframe_category": "long_term", "weight": 0.1, "features_used": 10, "model_purpose": "Portfolio allocation optimization"}
