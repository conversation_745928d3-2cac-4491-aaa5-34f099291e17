#!/usr/bin/env python3
"""
AI Trading Dashboard Server
Real-time web dashboard for monitoring the 9-model AI trading system.
"""

import os
import sys
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
import pandas as pd
import numpy as np
from threading import Thread, Lock
import sqlite3

# Import trading system components
from ai_model_manager import AIModelManager
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector
from trading_signal_generator import TradingSignalGenerator
from order_execution_system import OrderExecutionSystem

# Set up logging with DEBUG level to see data freshness
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class DashboardDataManager:
    """Manages real-time data for the dashboard."""
    
    def __init__(self):
        """Initialize the dashboard data manager."""
        self.data_lock = Lock()
        self.dashboard_data = {
            "system_status": "INITIALIZING",
            "last_update": datetime.now(),
            "price_data": {},
            "model_predictions": {},
            "trade_metrics": {},
            "system_health": {},
            "signal_history": [],
            "performance_data": {},
            "risk_metrics": {}
        }
        
        # Initialize trading components
        self.data_collector = None
        self.ai_manager = None
        self.pattern_detector = None
        self.signal_generator = None
        self.order_executor = None
        
        # Data update thread
        self.update_thread = None
        self.running = False
        
    def initialize_trading_system(self):
        """Initialize the trading system components."""
        try:
            logger.info("Initializing trading system for dashboard...")
            
            # Initialize components
            self.data_collector = SyntheticDataCollector()
            self.pattern_detector = SyntheticPatternDetector(self.data_collector)
            self.ai_manager = AIModelManager(self.data_collector, self.pattern_detector)
            self.signal_generator = TradingSignalGenerator(self.ai_manager, self.pattern_detector, self.data_collector)
            self.order_executor = OrderExecutionSystem(self.data_collector)

            # COLLECT SOME HISTORICAL DATA FIRST (models need data to analyze!)
            logger.info("Collecting recent historical data for analysis...")
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)  # Last 7 days
            self.data_collector.collect_historical_data(start_date, end_date)

            # START REAL-TIME DATA COLLECTION (This was missing!)
            self.data_collector.start_real_time_collection()
            logger.info("Real-time data collection started for dashboard")

            # Load all models
            logger.info("Loading AI models...")
            loaded_models = 0
            for model_name in self.ai_manager.model_configs.keys():
                if self.ai_manager.load_model(model_name):
                    loaded_models += 1
            
            logger.info(f"Loaded {loaded_models}/{len(self.ai_manager.model_configs)} models")
            
            with self.data_lock:
                self.dashboard_data["system_status"] = "OPERATIONAL"
                self.dashboard_data["models_loaded"] = loaded_models
                self.dashboard_data["total_models"] = len(self.ai_manager.model_configs)
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing trading system: {e}")
            with self.data_lock:
                self.dashboard_data["system_status"] = f"ERROR: {e}"
            return False
    
    def start_data_updates(self):
        """Start the data update thread."""
        if not self.running:
            self.running = True
            self.update_thread = Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            logger.info("Dashboard data update thread started")
    
    def stop_data_updates(self):
        """Stop the data update thread."""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("Dashboard data update thread stopped")
    
    def _update_loop(self):
        """Main data update loop."""
        while self.running:
            try:
                self._update_dashboard_data()
                time.sleep(30)  # Update every 30 seconds
            except Exception as e:
                logger.error(f"Error in dashboard update loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _update_dashboard_data(self):
        """Update all dashboard data."""
        try:
            with self.data_lock:
                # Update timestamp
                self.dashboard_data["last_update"] = datetime.now()
                
                # Update price data
                self._update_price_data()
                
                # Update model predictions
                self._update_model_predictions()
                
                # Update trade metrics
                self._update_trade_metrics()
                
                # Update system health
                self._update_system_health()
                
                # Update performance data
                self._update_performance_data()
                
                # Update risk metrics
                self._update_risk_metrics()
                
        except Exception as e:
            logger.error(f"Error updating dashboard data: {e}")
    
    def _update_price_data(self):
        """Update real-time price data."""
        try:
            if not self.data_collector:
                return
                
            # Get current price data for multiple timeframes
            timeframes = ['1M', '5M', '15M', '30M', '1H', '4H', '1D']
            price_data = {}
            
            for tf in timeframes:
                try:
                    # Convert timeframe string to minutes
                    tf_minutes = {'1M': 1, '5M': 5, '15M': 15, '30M': 30, '1H': 60, '4H': 240, '1D': 1440}.get(tf, 5)
                    data = self.data_collector.get_latest_data(tf_minutes, 100)
                    if data is not None and not data.empty:
                        # Convert to format suitable for charts
                        price_data[tf] = {
                            "timestamps": [str(ts) for ts in data.index.tolist()[-50:]],
                            "open": data['open'].tolist()[-50:],
                            "high": data['high'].tolist()[-50:],
                            "low": data['low'].tolist()[-50:],
                            "close": data['close'].tolist()[-50:],
                            "volume": data['tick_volume'].tolist()[-50:] if 'tick_volume' in data.columns else []
                        }
                except Exception as e:
                    logger.warning(f"Error getting {tf} data: {e}")
            
            self.dashboard_data["price_data"] = price_data
            
            # Get LIVE current price directly from MT5 (not stale database)
            try:
                live_price_data = self._get_live_mt5_price()
                if live_price_data:
                    self.dashboard_data["current_price"] = live_price_data
                else:
                    # Fallback to database if MT5 fails
                    current_data = self.data_collector.get_latest_data(1, 1)
                    if current_data is not None and not current_data.empty:
                        latest = current_data.iloc[-1]
                        self.dashboard_data["current_price"] = {
                            "price": float(latest['close']),
                            "change": float(latest['close'] - latest['open']),
                            "change_pct": float((latest['close'] - latest['open']) / latest['open'] * 100),
                            "timestamp": str(latest.name)
                        }
            except Exception as e:
                logger.warning(f"Error getting current price: {e}")
                
        except Exception as e:
            logger.error(f"Error updating price data: {e}")

    def _get_live_mt5_price(self):
        """Get live price directly from MT5 for real-time dashboard updates."""
        try:
            import MetaTrader5 as mt5

            # Get current tick
            tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
            if tick is None:
                return None

            current_price = (tick.bid + tick.ask) / 2

            # Get previous price for change calculation
            previous_price = getattr(self, '_last_price', current_price)
            self._last_price = current_price

            change = current_price - previous_price
            change_pct = (change / previous_price * 100) if previous_price > 0 else 0

            return {
                "price": float(current_price),
                "change": float(change),
                "change_pct": float(change_pct),
                "timestamp": datetime.fromtimestamp(tick.time).isoformat(),
                "bid": float(tick.bid),
                "ask": float(tick.ask),
                "spread": float(tick.ask - tick.bid),
                "live_update": True  # Flag to indicate this is live data
            }

        except Exception as e:
            logger.error(f"Error getting live MT5 price: {e}")
            return None

    def _update_model_predictions(self):
        """Update model predictions and analysis using SHARED prediction cache from live trading system."""
        try:
            if not self.ai_manager:
                return

            # Use OrderedDict to maintain Short → Medium → Long order
            from collections import OrderedDict
            model_predictions = OrderedDict()

            # Define the desired model order: Short → Medium → Long
            model_order = [
                # Short-term models
                "short_term_pattern_nn",
                "short_term_momentum_rf",
                "short_term_reversion_gb",
                # Medium-term models
                "medium_term_trend_lstm",
                "medium_term_breakout_rf",
                "medium_term_volatility_xgb",
                # Long-term models
                "long_term_macro_dnn",
                "long_term_levels_rf",
                "long_term_portfolio_gb"
            ]

            # FIRST: Try to get predictions from shared cache (live trading system)
            shared_predictions = self._get_shared_predictions()

            if shared_predictions:
                logger.debug("Using shared predictions from live trading system")
                # Use the SAME predictions that triggered live trades (in correct order)
                for model_name in model_order:
                    if model_name in shared_predictions:
                        pred_data = shared_predictions[model_name]
                        config = self.ai_manager.model_configs[model_name]
                        model_predictions[model_name] = {
                            "signal": pred_data["signal"],
                            "confidence": pred_data["confidence"],
                            "model_type": pred_data["model_type"],
                            "purpose": config["purpose"],
                            "timeframes": config["timeframes"],
                            "status": "ACTIVE",
                            "last_prediction": pred_data.get("timestamp", datetime.now().strftime('%H:%M:%S')),
                            "source": "LIVE_TRADING_CACHE"
                        }
                    elif model_name in self.ai_manager.model_configs:
                        # Model not in shared cache
                        config = self.ai_manager.model_configs[model_name]
                        model_predictions[model_name] = {
                            "status": "NO_PREDICTION",
                            "purpose": config["purpose"],
                            "source": "CACHE_MISS"
                        }
            else:
                logger.debug("No shared predictions found, generating dashboard predictions")
                # FALLBACK: Generate dashboard predictions (in correct order)
                for model_name in model_order:
                    try:
                        if model_name in self.ai_manager.models:
                            # Generate real features for this specific model
                            try:
                                features = self._generate_model_features(model_name)
                                if features is not None:
                                    prediction = self.ai_manager.predict(model_name, features)
                                else:
                                    prediction = None

                                if prediction:
                                    config = self.ai_manager.model_configs[model_name]
                                    model_predictions[model_name] = {
                                        "signal": prediction["signal"],
                                        "confidence": prediction["confidence"],
                                        "model_type": prediction["model_type"],
                                        "purpose": config["purpose"],
                                        "timeframes": config["timeframes"],
                                        "status": "ACTIVE",
                                        "last_prediction": datetime.now().strftime('%H:%M:%S'),
                                        "source": "DASHBOARD_GENERATED"
                                    }
                                else:
                                    model_predictions[model_name] = {
                                        "status": "NO_PREDICTION",
                                        "purpose": self.ai_manager.model_configs[model_name]["purpose"],
                                        "source": "DASHBOARD_GENERATED"
                                    }
                            except Exception as e:
                                model_predictions[model_name] = {
                                    "status": f"ERROR: {str(e)[:50]}",
                                    "purpose": self.ai_manager.model_configs[model_name]["purpose"],
                                    "source": "DASHBOARD_ERROR"
                                }
                        else:
                            model_predictions[model_name] = {
                                "status": "NOT_LOADED",
                                "purpose": self.ai_manager.model_configs[model_name]["purpose"],
                                "source": "NOT_LOADED"
                            }

                    except Exception as e:
                        model_predictions[model_name] = {
                            "status": f"ERROR: {str(e)[:50]}",
                            "purpose": self.ai_manager.model_configs.get(model_name, {}).get("purpose", "Unknown"),
                            "source": "DASHBOARD_ERROR"
                        }

            self.dashboard_data["model_predictions"] = model_predictions

            # Get timeframe-specific consensus using shared predictions if available
            try:
                if shared_predictions:
                    # Use shared predictions for consensus calculation
                    self._update_timeframe_consensus_from_shared(shared_predictions)
                else:
                    # Fallback to generating features and consensus
                    model_features = {}
                    for model_name in self.ai_manager.model_configs.keys():
                        if model_name in self.ai_manager.models:
                            features = self._generate_model_features(model_name)
                            if features is not None:
                                model_features[model_name] = features

                    # Get timeframe-specific consensus for each group (removed ensemble prediction)
                    self._update_timeframe_consensus(model_features)
            except Exception as e:
                logger.warning(f"Error getting timeframe consensus: {e}")

        except Exception as e:
            logger.error(f"Error updating model predictions: {e}")

    def _get_shared_predictions(self):
        """Get shared predictions from live trading system cache."""
        try:
            # Try to read from shared prediction cache file
            cache_file = "data/shared_predictions_cache.json"
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)

                # Check if cache is recent (within last 5 minutes)
                cache_timestamp = datetime.fromisoformat(cache_data.get("timestamp", "2000-01-01T00:00:00"))
                age_minutes = (datetime.now() - cache_timestamp).total_seconds() / 60

                if age_minutes <= 5:  # Use cache if less than 5 minutes old
                    logger.debug(f"Using shared prediction cache (age: {age_minutes:.1f} minutes)")
                    return cache_data.get("predictions", {})
                else:
                    logger.debug(f"Shared prediction cache too old: {age_minutes:.1f} minutes")

            return None

        except Exception as e:
            logger.warning(f"Error reading shared prediction cache: {e}")
            return None

    def _update_timeframe_consensus_from_shared(self, shared_predictions: dict):
        """Calculate consensus using shared predictions from live trading system."""
        try:
            # Define timeframe groups
            timeframe_groups = {
                "short_term": ["short_term_pattern_nn", "short_term_momentum_rf", "short_term_reversion_gb"],
                "medium_term": ["medium_term_trend_lstm", "medium_term_breakout_rf", "medium_term_volatility_xgb"],
                "long_term": ["long_term_macro_dnn", "long_term_levels_rf", "long_term_portfolio_gb"]
            }

            timeframe_consensus = {}

            for timeframe, model_names in timeframe_groups.items():
                # Get predictions for this timeframe group from shared cache
                group_predictions = {}
                for model_name in model_names:
                    if model_name in shared_predictions:
                        pred_data = shared_predictions[model_name]
                        group_predictions[model_name] = {
                            'signal': pred_data['signal'],
                            'confidence': pred_data['confidence']
                        }

                if group_predictions:
                    # Calculate consensus for this group
                    signals = [pred['signal'] for pred in group_predictions.values()]
                    confidences = [pred['confidence'] for pred in group_predictions.values()]

                    # Count signal types
                    buy_signals = len([s for s in signals if s > 0])
                    sell_signals = len([s for s in signals if s < 0])
                    hold_signals = len([s for s in signals if s == 0])
                    total_models = len(signals)

                    # Determine consensus
                    if buy_signals > sell_signals and buy_signals > hold_signals:
                        consensus_signal = "BUY"
                        consensus_strength = (buy_signals / total_models) * 100
                    elif sell_signals > buy_signals and sell_signals > hold_signals:
                        consensus_signal = "SELL"
                        consensus_strength = (sell_signals / total_models) * 100
                    else:
                        consensus_signal = "HOLD"
                        consensus_strength = (hold_signals / total_models) * 100

                    # Average confidence
                    avg_confidence = sum(confidences) / len(confidences)

                    # Check for ultra-strong signals (±2 with confidence ≥ 0.6)
                    ultra_strong_signals = []
                    # Check for timeframe consensus signals (±1 with confidence ≥ 0.7, need 2+ models)
                    consensus_signals = []

                    for model_name, pred in group_predictions.items():
                        # Ultra-strong signals (single model can trigger)
                        if abs(pred['signal']) == 2 and pred['confidence'] >= 0.6:
                            ultra_strong_signals.append({
                                'model': model_name,
                                'signal': pred['signal'],
                                'confidence': pred['confidence'],
                                'type': 'ULTRA_STRONG'
                            })
                        # Potential consensus signals
                        elif abs(pred['signal']) == 1 and pred['confidence'] >= 0.7:
                            consensus_signals.append({
                                'model': model_name,
                                'signal': pred['signal'],
                                'confidence': pred['confidence'],
                                'type': 'CONSENSUS'
                            })

                    # Check if we have 2+ consensus signals in same direction
                    strong_signals = ultra_strong_signals.copy()
                    if len(consensus_signals) >= 2:
                        buy_consensus = [s for s in consensus_signals if s['signal'] > 0]
                        sell_consensus = [s for s in consensus_signals if s['signal'] < 0]

                        if len(buy_consensus) >= 2:
                            strong_signals.extend(buy_consensus)
                        elif len(sell_consensus) >= 2:
                            strong_signals.extend(sell_consensus)

                    # Get timeframe trade counts from shared counter file
                    daily_trades = 0
                    monthly_trades = 0
                    try:
                        counter_file = "data/timeframe_counters.json"
                        if os.path.exists(counter_file):
                            with open(counter_file, 'r') as f:
                                counter_data = json.load(f)
                                daily_trades = counter_data.get('daily', {}).get(timeframe, 0)
                                monthly_trades = counter_data.get('monthly', {}).get(timeframe, 0)
                    except Exception as e:
                        logger.warning(f"Error reading shared counters: {e}")

                    timeframe_consensus[timeframe] = {
                        "consensus_signal": consensus_signal,
                        "consensus_strength": consensus_strength,
                        "avg_confidence": avg_confidence,
                        "contributing_models": total_models,
                        "strong_signals": strong_signals,
                        "model_breakdown": {
                            "buy": buy_signals,
                            "sell": sell_signals,
                            "hold": hold_signals
                        },
                        "daily_trades": daily_trades,
                        "monthly_trades": monthly_trades,
                        "source": "SHARED_CACHE"
                    }

            # Store in dashboard data
            self.dashboard_data["timeframe_consensus"] = timeframe_consensus

        except Exception as e:
            logger.error(f"Error calculating timeframe consensus from shared predictions: {e}")
    
    def _update_trade_metrics(self):
        """Update trading performance metrics."""
        try:
            if not self.order_executor:
                return

            # Get basic trade metrics
            metrics = {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "total_pnl": 0.0,
                "daily_pnl": 0.0,
                "max_drawdown": 0.0,
                "active_positions": 0,
                "account_balance": 10000.0,  # Default fallback
                "equity": 10000.0,
                "sharpe_ratio": 0.0,
                "profit_factor": 0.0,
                "avg_trade_pnl": 0.0
            }

            # Get real account information from MT5
            try:
                import MetaTrader5 as mt5
                account_info = mt5.account_info()

                if account_info:
                    # Update with real account data
                    metrics.update({
                        "account_balance": float(account_info.balance),
                        "equity": float(account_info.equity),
                        "margin": float(account_info.margin),
                        "free_margin": float(account_info.margin_free),
                    })
                    logger.debug(f"Updated account balance: ${account_info.balance:.2f}")
                else:
                    logger.warning("Could not get MT5 account info, using defaults")

                # Get execution statistics from order executor
                exec_stats = self.order_executor.get_execution_statistics()
                if exec_stats:
                    metrics.update({
                        "daily_pnl": exec_stats.get("daily_pnl", 0.0),
                        "active_positions": exec_stats.get("active_positions", 0),
                    })

                # Get detailed position summary for enhanced dashboard display
                position_summary = self.order_executor.get_position_summary()
                if position_summary:
                    metrics["position_details"] = position_summary

                # REMOVED: Historical P&L display - replaced with account balance
                # Instead of showing confusing historical P&L, just show account balance
                # The daily P&L is more important for daily trading decisions

                # Get basic trade statistics (without historical P&L confusion)
                historical_stats = self.order_executor.get_all_historical_trades_pnl()
                if historical_stats:
                    metrics.update({
                        "total_trades": historical_stats.get("total_trades", 0),
                        "winning_trades": historical_stats.get("winning_trades", 0),
                        "losing_trades": historical_stats.get("losing_trades", 0),
                        "win_rate": historical_stats.get("win_rate", 0.0) / 100.0,  # Convert to decimal
                    })
                    # NOTE: total_pnl now comes from account balance, not historical trades

                    logger.info(f"Dashboard updated with trade stats: {historical_stats.get('total_trades', 0)} trades, Account Balance: ${metrics.get('account_balance', 0.0):.2f}")

                # Calculate win rate if we have trade data (fallback)
                if exec_stats and metrics.get("win_rate", 0) == 0:
                    successful_fills = exec_stats.get("successful_fills", 0)
                    failed_fills = exec_stats.get("failed_fills", 0)
                    total_attempts = successful_fills + failed_fills

                    if total_attempts > 0:
                        metrics["win_rate"] = successful_fills / total_attempts

            except Exception as e:
                logger.warning(f"Error getting real account metrics: {e}")

            self.dashboard_data["trade_metrics"] = metrics

        except Exception as e:
            logger.error(f"Error updating trade metrics: {e}")
    
    def _update_system_health(self):
        """Update system health indicators."""
        try:
            health = {
                "mt5_connection": "UNKNOWN",
                "data_feed": "UNKNOWN",
                "models_status": "UNKNOWN",
                "memory_usage": 0,
                "cpu_usage": 0,
                "uptime": "00:00:00"
            }
            
            # Check MT5 connection
            try:
                if self.data_collector:
                    # Check if we can get data (indicates MT5 connection)
                    test_data = self.data_collector.get_latest_data(1, 1)
                    health["mt5_connection"] = "CONNECTED" if test_data is not None else "DISCONNECTED"
                else:
                    health["mt5_connection"] = "DISCONNECTED"
            except:
                health["mt5_connection"] = "ERROR"
            
            # Check data feed
            try:
                if self.data_collector:
                    # Try to get latest 1M data to check if feed is active
                    test_data = self.data_collector.get_latest_data(1, 1)
                    health["data_feed"] = "ACTIVE" if test_data is not None and not test_data.empty else "INACTIVE"
            except:
                health["data_feed"] = "ERROR"
            
            # Check models
            try:
                if self.ai_manager:
                    loaded_count = len(self.ai_manager.models)
                    total_count = len(self.ai_manager.model_configs)
                    health["models_status"] = f"{loaded_count}/{total_count} LOADED"
            except:
                health["models_status"] = "ERROR"
            
            # System resources (basic)
            try:
                import psutil
                health["memory_usage"] = psutil.virtual_memory().percent
                health["cpu_usage"] = psutil.cpu_percent()
            except ImportError:
                pass
            
            self.dashboard_data["system_health"] = health
            
        except Exception as e:
            logger.error(f"Error updating system health: {e}")
    
    def _update_performance_data(self):
        """Update model performance data."""
        try:
            if not self.ai_manager:
                return
                
            performance_report = self.ai_manager.get_performance_report()
            
            if performance_report and "models" in performance_report:
                perf_data = {}
                
                for model_name, model_data in performance_report["models"].items():
                    training_perf = model_data.get("training_performance", {})
                    live_perf = model_data.get("live_performance", {})
                    
                    perf_data[model_name] = {
                        "training_accuracy": training_perf.get("accuracy", 0.0),
                        "live_accuracy": live_perf.get("accuracy", 0.0),
                        "total_predictions": live_perf.get("total_predictions", 0),
                        "model_type": model_data.get("model_type", "unknown"),
                        "status": "LOADED" if model_data.get("loaded", False) else "NOT_LOADED"
                    }
                
                self.dashboard_data["performance_data"] = perf_data
                
                # Summary stats
                summary = performance_report.get("summary", {})
                self.dashboard_data["performance_summary"] = {
                    "average_accuracy": summary.get("average_accuracy", 0.0),
                    "total_predictions": summary.get("total_predictions", 0),
                    "models_with_data": summary.get("models_with_live_data", 0)
                }
                
        except Exception as e:
            logger.error(f"Error updating performance data: {e}")
    
    def _update_risk_metrics(self):
        """Update risk management metrics with REAL data from the trading system."""
        try:
            # Initialize with default values (simplified for Risk Management section only)
            risk_metrics = {
                "sl_tp_settings": {},
                "circuit_breakers": {}
            }

            # Set SL/TP Settings by timeframe (ORDERED: Short → Medium → Long)
            from collections import OrderedDict
            risk_metrics["sl_tp_settings"] = OrderedDict([
                ("short_term", {
                    "buy": {"sl_points": 50, "tp_points": 100, "sl_dollars": 0.50, "tp_dollars": 1.00},
                    "sell": {"sl_points": 100, "tp_points": 50, "sl_dollars": 1.00, "tp_dollars": 0.50}
                }),
                ("medium_term", {
                    "buy": {"sl_points": 200, "tp_points": 400, "sl_dollars": 2.00, "tp_dollars": 4.00},
                    "sell": {"sl_points": 150, "tp_points": 100, "sl_dollars": 1.50, "tp_dollars": 1.00}
                }),
                ("long_term", {
                    "buy": {"sl_points": 300, "tp_points": 600, "sl_dollars": 3.00, "tp_dollars": 6.00},
                    "sell": {"sl_points": 300, "tp_points": 100, "sl_dollars": 3.00, "tp_dollars": 1.00}
                })
            ])

            # Set Circuit breakers and limits (UPDATED DAILY LIMITS)
            risk_metrics["circuit_breakers"] = {
                "max_daily_loss": "$10.00",
                "max_daily_profit": "$20.00",
                "max_concurrent": "3 positions",
                "drawdown_limit": "50%",
                "one_per_timeframe": "1 trade per timeframe",
                "mt5_connection": "Required"
            }

            self.dashboard_data["risk_metrics"] = risk_metrics

        except Exception as e:
            logger.error(f"Error updating risk metrics: {e}")

    def _update_timeframe_consensus(self, model_features: dict):
        """Calculate consensus for each timeframe group separately."""
        try:
            # Define timeframe groups
            timeframe_groups = {
                "short_term": ["short_term_pattern_nn", "short_term_momentum_rf", "short_term_reversion_gb"],
                "medium_term": ["medium_term_trend_lstm", "medium_term_breakout_rf", "medium_term_volatility_xgb"],
                "long_term": ["long_term_macro_dnn", "long_term_levels_rf", "long_term_portfolio_gb"]
            }

            timeframe_consensus = {}

            for timeframe, model_names in timeframe_groups.items():
                # Get predictions for this timeframe group
                group_predictions = {}
                for model_name in model_names:
                    if model_name in model_features and model_name in self.ai_manager.models:
                        prediction = self.ai_manager.predict(model_name, model_features[model_name])
                        if prediction:
                            group_predictions[model_name] = prediction

                if group_predictions:
                    # Calculate consensus for this group
                    signals = [pred['signal'] for pred in group_predictions.values()]
                    confidences = [pred['confidence'] for pred in group_predictions.values()]

                    # Count signal types
                    buy_signals = len([s for s in signals if s > 0])
                    sell_signals = len([s for s in signals if s < 0])
                    hold_signals = len([s for s in signals if s == 0])
                    total_models = len(signals)

                    # Determine consensus
                    if buy_signals > sell_signals and buy_signals > hold_signals:
                        consensus_signal = "BUY"
                        consensus_strength = (buy_signals / total_models) * 100
                    elif sell_signals > buy_signals and sell_signals > hold_signals:
                        consensus_signal = "SELL"
                        consensus_strength = (sell_signals / total_models) * 100
                    else:
                        consensus_signal = "HOLD"
                        consensus_strength = (hold_signals / total_models) * 100

                    # Average confidence
                    avg_confidence = sum(confidences) / len(confidences)

                    # Check for ultra-strong signals (±2 with confidence ≥ 0.6)
                    ultra_strong_signals = []
                    # Check for timeframe consensus signals (±1 with confidence ≥ 0.7, need 2+ models)
                    consensus_signals = []

                    for model_name, pred in group_predictions.items():
                        # Ultra-strong signals (single model can trigger)
                        if abs(pred['signal']) == 2 and pred['confidence'] >= 0.6:
                            ultra_strong_signals.append({
                                'model': model_name,
                                'signal': pred['signal'],
                                'confidence': pred['confidence'],
                                'type': 'ULTRA_STRONG'
                            })
                        # Potential consensus signals
                        elif abs(pred['signal']) == 1 and pred['confidence'] >= 0.7:
                            consensus_signals.append({
                                'model': model_name,
                                'signal': pred['signal'],
                                'confidence': pred['confidence'],
                                'type': 'CONSENSUS'
                            })

                    # Check if we have 2+ consensus signals in same direction
                    strong_signals = ultra_strong_signals.copy()
                    if len(consensus_signals) >= 2:
                        buy_consensus = [s for s in consensus_signals if s['signal'] > 0]
                        sell_consensus = [s for s in consensus_signals if s['signal'] < 0]

                        if len(buy_consensus) >= 2:
                            strong_signals.extend(buy_consensus)
                        elif len(sell_consensus) >= 2:
                            strong_signals.extend(sell_consensus)

                    # Get timeframe trade counts from shared counter file
                    daily_trades = 0
                    monthly_trades = 0
                    try:
                        import json
                        import os
                        counter_file = "data/timeframe_counters.json"
                        if os.path.exists(counter_file):
                            with open(counter_file, 'r') as f:
                                counter_data = json.load(f)
                                daily_trades = counter_data.get('daily', {}).get(timeframe, 0)
                                monthly_trades = counter_data.get('monthly', {}).get(timeframe, 0)
                    except Exception as e:
                        logger.warning(f"Error reading shared counters: {e}")
                        # Fallback to order executor if available
                        if hasattr(self, 'order_executor') and self.order_executor:
                            daily_trades = self.order_executor.timeframe_daily_trades.get(timeframe, 0)
                            monthly_trades = self.order_executor.timeframe_monthly_trades.get(timeframe, 0)

                    timeframe_consensus[timeframe] = {
                        "consensus_signal": consensus_signal,
                        "consensus_strength": consensus_strength,
                        "avg_confidence": avg_confidence,
                        "contributing_models": total_models,
                        "strong_signals": strong_signals,
                        "model_breakdown": {
                            "buy": buy_signals,
                            "sell": sell_signals,
                            "hold": hold_signals
                        },
                        "daily_trades": daily_trades,
                        "monthly_trades": monthly_trades
                    }

            # Store in dashboard data
            self.dashboard_data["timeframe_consensus"] = timeframe_consensus

        except Exception as e:
            logger.error(f"Error calculating timeframe consensus: {e}")

    def _generate_model_features(self, model_name: str):
        """Generate proper features for a specific model using UNIFIED feature extraction."""
        try:
            if not self.data_collector or not self.pattern_detector or not self.ai_manager:
                return None

            config = self.ai_manager.model_configs[model_name]
            timeframes = config["timeframes"]
            feature_types = config["features"]
            lookback = config["lookback"]

            # Get data for the primary timeframe
            primary_tf = timeframes[0]  # Use first timeframe as primary
            df = self.data_collector.get_latest_data(primary_tf, lookback + 50)

            if df is None or df.empty or len(df) < lookback:
                return None

            # DEBUG: Log data freshness to see if data is changing
            if not df.empty:
                latest_timestamp = df.index[-1]
                latest_price = df['close'].iloc[-1]
                logger.debug(f"[{model_name}] Latest data: {latest_timestamp}, Price: {latest_price:.2f}")

            # Calculate synthetic indicators
            df_with_indicators = self.pattern_detector.calculate_synthetic_indicators(df)
            latest_row = df_with_indicators.iloc[-1]

            # Use the SAME feature extraction as the live trading system
            # Reuse existing signal generator instead of creating new one
            if not self.signal_generator:
                from trading_signal_generator import TradingSignalGenerator
                self.signal_generator = TradingSignalGenerator(self.ai_manager, self.pattern_detector, self.data_collector)

            temp_signal_gen = self.signal_generator

            # Generate features based on model configuration using UNIFIED methods
            feature_vector = []

            # Extract features based on model's feature types using SAME methods as live system
            if "price_action" in feature_types:
                feature_vector.extend(temp_signal_gen._get_price_action_features(df_with_indicators, latest_row))

            if "volatility_regime" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_regime_features(df_with_indicators, latest_row))

            if "micro_patterns" in feature_types:
                feature_vector.extend(temp_signal_gen._get_micro_pattern_features(df_with_indicators, latest_row))

            if "momentum" in feature_types:
                feature_vector.extend(temp_signal_gen._get_momentum_features(df_with_indicators, latest_row))

            if "rsi" in feature_types:
                feature_vector.extend(temp_signal_gen._get_rsi_features(df_with_indicators, latest_row))

            if "macd" in feature_types:
                feature_vector.extend(temp_signal_gen._get_macd_features(df_with_indicators, latest_row))

            if "volatility_adjusted_momentum" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_adjusted_momentum_features(df_with_indicators, latest_row))

            if "deviation" in feature_types:
                feature_vector.extend(temp_signal_gen._get_deviation_features(df_with_indicators, latest_row))

            if "bollinger" in feature_types:
                feature_vector.extend(temp_signal_gen._get_bollinger_features(df_with_indicators, latest_row))

            if "rsi_extreme" in feature_types:
                feature_vector.extend(temp_signal_gen._get_rsi_extreme_features(df_with_indicators, latest_row))

            if "trend_strength" in feature_types:
                feature_vector.extend(temp_signal_gen._get_trend_strength_features(df_with_indicators, latest_row))

            if "moving_averages" in feature_types:
                feature_vector.extend(temp_signal_gen._get_moving_average_features(df_with_indicators, latest_row))

            if "trend_sustainability" in feature_types:
                feature_vector.extend(temp_signal_gen._get_trend_sustainability_features(df_with_indicators, latest_row))

            if "support_resistance" in feature_types:
                feature_vector.extend(temp_signal_gen._get_support_resistance_features(df_with_indicators, latest_row))

            if "false_breakout_filter" in feature_types:
                feature_vector.extend(temp_signal_gen._get_false_breakout_filter_features(df_with_indicators, latest_row))

            if "volatility" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_features(df_with_indicators, latest_row))

            if "volatility_regime" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_regime_features(df_with_indicators, latest_row))

            if "atr" in feature_types:
                feature_vector.extend(temp_signal_gen._get_atr_features(df_with_indicators, latest_row))

            if "bollinger_width" in feature_types:
                feature_vector.extend(temp_signal_gen._get_bollinger_width_features(df_with_indicators, latest_row))

            # Always add synthetic-specific features (same as live system)
            feature_vector.extend(temp_signal_gen._get_synthetic_features(df_with_indicators, latest_row))

            # Ensure we have valid features
            if len(feature_vector) == 0 or any(np.isnan(feature_vector)):
                return None

            return np.array(feature_vector).reshape(1, -1)

        except Exception as e:
            logger.warning(f"Error generating features for {model_name}: {e}")
            return None

    def _extract_price_action_features(self, window):
        """Extract price action features."""
        if window.empty:
            return [0.0] * 7

        features = []

        # Basic price features
        features.append(window['close'].iloc[-1] / window['close'].iloc[0] - 1)  # Total return
        features.append(window['high'].max() / window['close'].iloc[-1] - 1)     # High deviation
        features.append(window['low'].min() / window['close'].iloc[-1] - 1)      # Low deviation
        features.append(window['tick_volume'].mean() if 'tick_volume' in window.columns else 0.0)  # Average volume

        # Price patterns
        features.append(len(window[window['close'] > window['open']]) / len(window))  # Bullish ratio
        features.append(window['close'].std() / window['close'].mean())               # Price volatility

        # Recent momentum
        if len(window) >= 5:
            features.append(window['close'].iloc[-5:].mean() / window['close'].iloc[-10:-5].mean() - 1)
        else:
            features.append(0.0)

        return features

    def _extract_clean_volatility_features(self, window):
        """Extract clean volatility features (replaces volume features)."""
        if window.empty:
            return [0.0] * 3

        features = []

        # ATR normalized by price
        if len(window) >= 14:
            high_low_diff = window['high'] - window['low']
            atr = high_low_diff.rolling(14).mean().iloc[-1]
            atr_normalized = atr / window['close'].iloc[-1] if window['close'].iloc[-1] > 0 else 0.0
            features.append(atr_normalized)
        else:
            features.append(0.0)

        # Bollinger Bandwidth
        if len(window) >= 20:
            bb_mean = window['close'].rolling(20).mean()
            bb_std = window['close'].rolling(20).std()
            bb_bandwidth = (2 * bb_std.iloc[-1]) / bb_mean.iloc[-1] if bb_mean.iloc[-1] > 0 else 0.0
            features.append(bb_bandwidth)
        else:
            features.append(0.0)

        # Volatility percentile
        if len(window) >= 20:
            volatility = window['close'].rolling(5).std()
            vol_q20 = volatility.quantile(0.2)
            vol_q80 = volatility.quantile(0.8)
            if vol_q80 > vol_q20:
                vol_percentile = (volatility.iloc[-1] - vol_q20) / (vol_q80 - vol_q20)
                vol_percentile = max(0, min(1, vol_percentile))
            else:
                vol_percentile = 0.5
            features.append(vol_percentile)
        else:
            features.append(0.5)

        return features

    def _extract_momentum_features(self, window):
        """Extract momentum features."""
        if window.empty:
            return [0.0] * 8

        features = []

        # Calculate returns if not present
        if 'returns' not in window.columns:
            window = window.copy()
            window['returns'] = window['close'].pct_change()

        # RSI-like calculation
        gains = window['returns'].where(window['returns'] > 0, 0)
        losses = -window['returns'].where(window['returns'] < 0, 0)

        avg_gain = gains.rolling(window=14, min_periods=1).mean().iloc[-1]
        avg_loss = losses.rolling(window=14, min_periods=1).mean().iloc[-1]

        if avg_loss != 0:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
        else:
            rsi = 100 if avg_gain > 0 else 50

        features.append(rsi / 100)  # Normalize to 0-1

        # MACD-like calculation
        if len(window) >= 26:
            ema12 = window['close'].ewm(span=12).mean().iloc[-1]
            ema26 = window['close'].ewm(span=26).mean().iloc[-1]
            macd = (ema12 - ema26) / window['close'].iloc[-1]
            features.append(macd)
        else:
            features.append(0.0)

        # Price momentum
        if len(window) >= 10:
            momentum_5 = window['close'].iloc[-1] / window['close'].iloc[-5] - 1
            momentum_10 = window['close'].iloc[-1] / window['close'].iloc[-10] - 1
            features.extend([momentum_5, momentum_10])
        else:
            features.extend([0.0, 0.0])

        # Acceleration features
        if 'price_acceleration' in window.columns:
            features.append(window['price_acceleration'].iloc[-1])
            features.append(window['price_acceleration'].mean())
            features.append(window['price_acceleration'].std())
        else:
            features.extend([0.0, 0.0, 0.0])

        return features

    def _extract_volatility_features(self, window):
        """Extract volatility features."""
        if window.empty:
            return [0.0] * 4

        features = []

        # ATR calculation
        if len(window) >= 14:
            high_low = window['high'] - window['low']
            high_close = abs(window['high'] - window['close'].shift(1))
            low_close = abs(window['low'] - window['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(window=14).mean().iloc[-1]
            features.append(atr / window['close'].iloc[-1])
        else:
            features.append(0.0)

        # Volatility regime
        if 'volatility_compression' in window.columns:
            features.append(window['volatility_compression'].iloc[-1])
        else:
            features.append(0.0)

        # Bollinger band width
        if len(window) >= 20:
            sma = window['close'].rolling(window=20).mean().iloc[-1]
            std = window['close'].rolling(window=20).std().iloc[-1]
            bb_width = (std * 2) / sma
            features.append(bb_width)
        else:
            features.append(0.0)

        # Recent volatility
        if len(window) >= 10:
            recent_vol = window['close'].iloc[-10:].std() / window['close'].iloc[-10:].mean()
            features.append(recent_vol)
        else:
            features.append(0.0)

        return features

    def _extract_trend_features(self, window):
        """Extract trend features."""
        if window.empty:
            return [0.0] * 5

        features = []

        # Moving average trends
        if len(window) >= 20:
            ma_short = window['close'].rolling(window=5).mean().iloc[-1]
            ma_long = window['close'].rolling(window=20).mean().iloc[-1]
            features.append((ma_short - ma_long) / window['close'].iloc[-1])
        else:
            features.append(0.0)

        # Trend strength
        if len(window) >= 10:
            trend_up = len(window[window['close'] > window['close'].shift(1)]) / len(window)
            features.append(trend_up)
        else:
            features.append(0.5)

        # EMA relationships
        if len(window) >= 12:
            ema_fast = window['close'].ewm(span=12).mean().iloc[-1]
            ema_slow = window['close'].ewm(span=26).mean().iloc[-1]
            features.append((ema_fast - ema_slow) / window['close'].iloc[-1])
        else:
            features.append(0.0)

        # Volume trend
        if 'tick_volume' in window.columns and len(window) >= 10:
            vol_trend = window['tick_volume'].iloc[-5:].mean() / window['tick_volume'].iloc[-10:-5].mean() - 1
            features.append(vol_trend)
        else:
            features.append(0.0)

        # Linear regression slope
        if len(window) >= 10:
            x = np.arange(len(window))
            y = window['close'].values
            slope = np.polyfit(x, y, 1)[0]
            features.append(slope / window['close'].iloc[-1])
        else:
            features.append(0.0)

        return features

    def _extract_micro_pattern_features(self, window):
        """Extract micro pattern features."""
        if window.empty:
            return [0.0] * 5

        features = []

        # Recent price patterns
        if len(window) >= 5:
            recent_highs = window['high'].iloc[-5:].max()
            recent_lows = window['low'].iloc[-5:].min()
            current_close = window['close'].iloc[-1]

            if recent_highs != recent_lows:
                features.append((current_close - recent_lows) / (recent_highs - recent_lows))
            else:
                features.append(0.5)

            features.append((window['high'].iloc[-1] - window['low'].iloc[-1]) / current_close)
        else:
            features.extend([0.5, 0.0])

        # Candlestick patterns
        if len(window) >= 3:
            # Doji-like pattern
            body_size = abs(window['close'] - window['open']) / (window['high'] - window['low'])
            features.append(body_size.iloc[-1])

            # Upper/lower shadows
            upper_shadow = (window['high'] - window[['open', 'close']].max(axis=1)) / (window['high'] - window['low'])
            lower_shadow = (window[['open', 'close']].min(axis=1) - window['low']) / (window['high'] - window['low'])

            features.append(upper_shadow.iloc[-1])
            features.append(lower_shadow.iloc[-1])
        else:
            features.extend([0.0, 0.0, 0.0])

        return features

    def _extract_deviation_features(self, window):
        """Extract deviation and bollinger features."""
        if window.empty:
            return [0.0] * 4

        features = []

        # Bollinger bands
        if len(window) >= 20:
            sma = window['close'].rolling(window=20).mean().iloc[-1]
            std = window['close'].rolling(window=20).std().iloc[-1]
            current_price = window['close'].iloc[-1]

            # Position within bands
            upper_band = sma + (2 * std)
            lower_band = sma - (2 * std)

            if upper_band != lower_band:
                bb_position = (current_price - lower_band) / (upper_band - lower_band)
            else:
                bb_position = 0.5

            features.append(bb_position)
            features.append((current_price - sma) / sma)  # Deviation from mean
        else:
            features.extend([0.5, 0.0])

        # RSI extremes
        if 'returns' in window.columns or len(window) >= 14:
            if 'returns' not in window.columns:
                returns = window['close'].pct_change()
            else:
                returns = window['returns']

            gains = returns.where(returns > 0, 0)
            losses = -returns.where(returns < 0, 0)

            avg_gain = gains.rolling(window=14, min_periods=1).mean().iloc[-1]
            avg_loss = losses.rolling(window=14, min_periods=1).mean().iloc[-1]

            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50

            # RSI extreme levels
            features.append(1.0 if rsi > 70 else (1.0 if rsi < 30 else 0.0))  # Extreme flag
            features.append(rsi / 100)  # Normalized RSI
        else:
            features.extend([0.0, 0.5])

        return features

    def _extract_levels_features(self, window):
        """Extract support/resistance level features."""
        if window.empty:
            return [0.0] * 4

        features = []
        current_price = window['close'].iloc[-1]

        # Recent highs and lows as support/resistance
        if len(window) >= 20:
            recent_highs = window['high'].rolling(window=5).max()
            recent_lows = window['low'].rolling(window=5).min()

            # Distance to nearest resistance
            resistance_levels = recent_highs[recent_highs > current_price]
            if not resistance_levels.empty:
                nearest_resistance = resistance_levels.min()
                features.append((nearest_resistance - current_price) / current_price)
            else:
                features.append(0.0)

            # Distance to nearest support
            support_levels = recent_lows[recent_lows < current_price]
            if not support_levels.empty:
                nearest_support = support_levels.max()
                features.append((current_price - nearest_support) / current_price)
            else:
                features.append(0.0)
        else:
            features.extend([0.0, 0.0])

        # Fibonacci-like levels
        if len(window) >= 50:
            period_high = window['high'].max()
            period_low = window['low'].min()

            if period_high != period_low:
                fib_236 = period_low + 0.236 * (period_high - period_low)
                fib_618 = period_low + 0.618 * (period_high - period_low)

                # Distance to key fib levels
                features.append(abs(current_price - fib_236) / current_price)
                features.append(abs(current_price - fib_618) / current_price)
            else:
                features.extend([0.0, 0.0])
        else:
            features.extend([0.0, 0.0])

        return features

    def _extract_synthetic_features(self, window):
        """Extract synthetic-specific features."""
        features = []

        # Synthetic-specific indicators
        synthetic_cols = ['jumpiness_score', 'volatility_compression', 'price_acceleration',
                         'tick_velocity', 'mean_reversion_signal', 'regime_state']

        for col in synthetic_cols:
            if col in window.columns:
                features.append(window[col].iloc[-1] if not pd.isna(window[col].iloc[-1]) else 0.0)
                features.append(window[col].mean() if not pd.isna(window[col].mean()) else 0.0)
            else:
                features.extend([0.0, 0.0])

        return features

    def get_dashboard_data(self):
        """Get current dashboard data."""
        with self.data_lock:
            data = self.dashboard_data.copy()

            # Debug: Log timeframe consensus structure
            if "timeframe_consensus" in data:
                logger.info("=== TIMEFRAME CONSENSUS DEBUG ===")
                for timeframe, consensus in data["timeframe_consensus"].items():
                    logger.info(f"{timeframe}: {consensus.get('consensus_signal', 'N/A')} ({consensus.get('consensus_strength', 0):.1f}%)")
                logger.info("=== END DEBUG ===")

            return data

# Initialize Flask app
app = Flask(__name__, template_folder='dashboard/templates', static_folder='dashboard/static')
app.secret_key = 'ai_trading_dashboard_2024'

# Global dashboard manager
dashboard_manager = DashboardDataManager()

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('dashboard.html')

@app.route('/api/data')
def get_data():
    """API endpoint for dashboard data."""
    return jsonify(dashboard_manager.get_dashboard_data())

@app.route('/api/health')
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "OK",
        "timestamp": datetime.now().isoformat(),
        "system_status": dashboard_manager.get_dashboard_data().get("system_status", "UNKNOWN")
    })

def main():
    """Main function to start the dashboard server."""
    try:
        print("🚀 Starting AI Trading Dashboard Server...")
        
        # Initialize trading system
        if dashboard_manager.initialize_trading_system():
            print("✅ Trading system initialized successfully")
        else:
            print("❌ Trading system initialization failed")
            return False
        
        # Start data updates
        dashboard_manager.start_data_updates()
        print("✅ Data update thread started")
        
        # Start Flask server
        print("🌐 Starting web server on http://localhost:5000")
        print("📊 Dashboard will be available at: http://localhost:5000")
        print("🔄 Auto-refresh every 3 minutes")
        print("Press Ctrl+C to stop")
        
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        print("\n⚠️ Dashboard server stopped by user")
    except Exception as e:
        print(f"❌ Error starting dashboard server: {e}")
        return False
    finally:
        dashboard_manager.stop_data_updates()
        print("✅ Dashboard server shutdown complete")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
