"""
Long-term trend analysis model for the DEX 900 DOWN Index.
This model focuses on analyzing trends in longer timeframes (weeks to months).
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import logging
from typing import Tuple, List, Dict, Any, Optional

import sys
sys.path.append('..')
import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/long_term_model.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("LongTermModel")

class LongTermModel:
    """Model for long-term trend analysis."""

    def __init__(self):
        """Initialize the long-term model."""
        self.config = config
        self.model_settings = self.config.MODEL_SETTINGS["long_term"]
        self.model = None
        self.ensure_directories()

    def ensure_directories(self):
        """Ensure necessary directories exist."""
        os.makedirs("models/saved", exist_ok=True)
        os.makedirs("logs", exist_ok=True)

    def build_model(self, input_shape: Tuple[int, int]) -> None:
        """
        Build the model architecture.

        Args:
            input_shape: Shape of the input data (sequence_length, features)
        """
        model_type = self.model_settings["model_type"]
        layers = self.model_settings["layers"]
        dropout = self.model_settings["dropout"]

        # Create model using Keras functional API to avoid warnings
        from tensorflow.keras.layers import Input
        from tensorflow.keras.models import Model

        # Define input layer explicitly
        inputs = Input(shape=(input_shape[0], input_shape[1]))

        # Add recurrent layers based on model type
        if model_type == "lstm":
            # First layer
            x = LSTM(layers[0], return_sequences=len(layers) > 1)(inputs)
            x = BatchNormalization()(x)
            x = Dropout(dropout)(x)

            # Add additional layers if specified
            for i in range(1, len(layers)):
                return_sequences = i < len(layers) - 1
                x = LSTM(layers[i], return_sequences=return_sequences)(x)
                x = BatchNormalization()(x)
                x = Dropout(dropout)(x)

        elif model_type == "gru":
            # First layer
            x = GRU(layers[0], return_sequences=len(layers) > 1)(inputs)
            x = BatchNormalization()(x)
            x = Dropout(dropout)(x)

            # Add additional layers if specified
            for i in range(1, len(layers)):
                return_sequences = i < len(layers) - 1
                x = GRU(layers[i], return_sequences=return_sequences)(x)
                x = BatchNormalization()(x)
                x = Dropout(dropout)(x)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")

        # Add output layer
        outputs = Dense(1, activation='sigmoid')(x)

        # Create and compile the model
        model = Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        self.model = model
        logger.info(f"Built {model_type.upper()} model with {len(layers)} layers")

    def train(self, X_train: np.ndarray, y_train: np.ndarray,
             X_val: Optional[np.ndarray] = None,
             y_val: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Train the model.

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets

        Returns:
            Training history
        """
        if self.model is None:
            self.build_model(input_shape=(X_train.shape[1], X_train.shape[2]))

        # Set up callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=self.model_settings["patience"],
                restore_best_weights=True
            ),
            ModelCheckpoint(
                filepath="models/saved/long_term_best.h5",
                monitor='val_loss',
                save_best_only=True
            )
        ]

        # Train the model
        history = self.model.fit(
            X_train, y_train,
            epochs=self.model_settings["epochs"],
            batch_size=self.model_settings["batch_size"],
            validation_data=(X_val, y_val) if X_val is not None and y_val is not None else None,
            callbacks=callbacks,
            verbose=1
        )

        logger.info(f"Model trained for {len(history.history['loss'])} epochs")
        return history.history

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the model.

        Args:
            X: Input features

        Returns:
            Predicted probabilities
        """
        if self.model is None:
            raise ValueError("Model has not been built or loaded yet")

        return self.model.predict(X)

    def save(self, filepath: Optional[str] = None) -> None:
        """
        Save the model.

        Args:
            filepath: Path to save the model to
        """
        if self.model is None:
            raise ValueError("No model to save")

        if filepath is None:
            filepath = "models/saved/long_term_latest.h5"

        self.model.save(filepath)
        logger.info(f"Model saved to {filepath}")

    def load(self, filepath: str) -> None:
        """
        Load a saved model.

        Args:
            filepath: Path to the saved model
        """
        self.model = load_model(filepath)
        logger.info(f"Model loaded from {filepath}")

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        Evaluate the model.

        Args:
            X_test: Test features
            y_test: Test targets

        Returns:
            Dictionary of evaluation metrics
        """
        if self.model is None:
            raise ValueError("Model has not been built or loaded yet")

        loss, accuracy = self.model.evaluate(X_test, y_test)

        # Make predictions
        y_pred = (self.predict(X_test) > 0.5).astype(int).flatten()

        # Calculate additional metrics
        true_positives = np.sum((y_pred == 1) & (y_test == 1))
        false_positives = np.sum((y_pred == 1) & (y_test == 0))
        true_negatives = np.sum((y_pred == 0) & (y_test == 0))
        false_negatives = np.sum((y_pred == 0) & (y_test == 1))

        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        metrics = {
            'loss': loss,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }

        logger.info(f"Model evaluation: {metrics}")
        return metrics
