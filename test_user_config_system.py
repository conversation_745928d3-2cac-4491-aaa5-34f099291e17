#!/usr/bin/env python3
"""
Test script for the User Configuration System
Tests manual lot size configuration and integration with order execution.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('UserConfigTest')

def test_user_config_manager():
    """Test the UserConfigManager functionality."""
    print("\n🧪 TEST 1: User Configuration Manager")
    print("-" * 40)
    
    try:
        from user_config_manager import UserConfigManager
        
        # Test initialization
        config_manager = UserConfigManager()
        print("✅ UserConfigManager initialized successfully")
        
        # Test default configuration
        default_config = config_manager.default_config
        print(f"✅ Default config loaded: {len(default_config)} sections")
        
        # Test configuration loading (should return None if no config exists)
        existing_config = config_manager.load_user_config()
        if existing_config:
            print(f"✅ Found existing configuration with {len(existing_config)} sections")
        else:
            print("ℹ️ No existing configuration found (expected for first run)")
        
        # Test lot size configuration retrieval
        lot_config = config_manager.get_lot_size_config()
        print(f"✅ Lot size config: mode={lot_config.get('mode', 'auto')}")
        
        # Test manual lot size retrieval
        manual_lot = config_manager.get_manual_lot_size()
        if manual_lot:
            print(f"✅ Manual lot size: {manual_lot}")
        else:
            print("ℹ️ No manual lot size configured (using auto mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ UserConfigManager test failed: {e}")
        return False

def test_order_execution_integration():
    """Test integration with order execution system."""
    print("\n🧪 TEST 2: Order Execution Integration")
    print("-" * 40)
    
    try:
        # Create a test configuration
        test_config = {
            "lot_size": {
                "mode": "manual",
                "manual_lot_size": 0.05,
                "last_updated": datetime.now().isoformat()
            },
            "system_info": {
                "created": datetime.now().isoformat(),
                "version": "2.1"
            }
        }
        
        # Save test configuration
        with open("user_config.json", 'w') as f:
            json.dump(test_config, f, indent=2)
        print("✅ Test configuration created")
        
        # Test order execution system can read the configuration
        try:
            from order_execution_system import OrderExecutionSystem
            from synthetic_data_collector import SyntheticDataCollector
            
            # Create minimal components for testing
            data_collector = SyntheticDataCollector()
            order_system = OrderExecutionSystem(data_collector)
            
            # Test manual lot size retrieval
            manual_lot = order_system._get_manual_lot_size()
            if manual_lot == 0.05:
                print(f"✅ Order system correctly read manual lot size: {manual_lot}")
            else:
                print(f"❌ Order system read incorrect lot size: {manual_lot}")
                return False
            
            # Test configuration source logging
            print("✅ Configuration source logging available")
            
            # Test volume validation
            validated_volume = order_system._validate_volume(0.05)
            print(f"✅ Volume validation works: {validated_volume}")
            
            return True
            
        except Exception as e:
            print(f"❌ Order execution integration test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Integration test setup failed: {e}")
        return False

def test_configuration_file_handling():
    """Test configuration file creation and validation."""
    print("\n🧪 TEST 3: Configuration File Handling")
    print("-" * 40)
    
    try:
        # Test configuration file creation
        test_config = {
            "lot_size": {
                "mode": "manual",
                "manual_lot_size": 0.02,
                "last_updated": datetime.now().isoformat()
            },
            "risk_settings": {
                "max_risk_per_trade": 0.01,
                "use_custom_risk": True
            },
            "system_info": {
                "created": datetime.now().isoformat(),
                "version": "2.1"
            }
        }
        
        # Save configuration
        config_file = "test_user_config.json"
        with open(config_file, 'w') as f:
            json.dump(test_config, f, indent=2)
        print("✅ Configuration file created successfully")
        
        # Test configuration loading
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        
        if loaded_config == test_config:
            print("✅ Configuration file loaded correctly")
        else:
            print("❌ Configuration file content mismatch")
            return False
        
        # Test configuration validation
        if "lot_size" in loaded_config:
            lot_config = loaded_config["lot_size"]
            if lot_config.get("mode") == "manual":
                manual_lot = lot_config.get("manual_lot_size")
                if manual_lot and manual_lot > 0:
                    print(f"✅ Configuration validation passed: {manual_lot}")
                else:
                    print("❌ Invalid manual lot size in configuration")
                    return False
            else:
                print("✅ Auto mode configuration valid")
        else:
            print("❌ Missing lot_size section in configuration")
            return False
        
        # Cleanup test file
        os.remove(config_file)
        print("✅ Test configuration file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration file handling test failed: {e}")
        return False

def test_startup_integration():
    """Test integration with startup system."""
    print("\n🧪 TEST 4: Startup Integration")
    print("-" * 40)
    
    try:
        # Test that startup script can import required modules
        try:
            from user_config_manager import UserConfigManager
            print("✅ UserConfigManager import successful")
        except ImportError as e:
            print(f"❌ UserConfigManager import failed: {e}")
            return False
        
        # Test configuration validation function
        import json
        
        # Create valid test config
        valid_config = {
            "lot_size": {
                "mode": "manual",
                "manual_lot_size": 0.03
            }
        }
        
        with open("user_config.json", 'w') as f:
            json.dump(valid_config, f, indent=2)
        
        # Test validation logic
        if os.path.exists("user_config.json"):
            with open("user_config.json", 'r') as f:
                config = json.load(f)
            
            if "lot_size" in config:
                lot_config = config["lot_size"]
                mode = lot_config.get("mode", "auto")
                
                if mode == "manual":
                    manual_lot = lot_config.get("manual_lot_size")
                    if manual_lot and manual_lot > 0:
                        print(f"✅ Startup validation logic works: {manual_lot}")
                    else:
                        print("❌ Startup validation failed: invalid manual lot size")
                        return False
                else:
                    print("✅ Startup validation works for auto mode")
            else:
                print("❌ Startup validation failed: missing lot_size section")
                return False
        else:
            print("❌ Configuration file not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Startup integration test failed: {e}")
        return False

def cleanup_test_files():
    """Clean up any test files created during testing."""
    test_files = ["user_config.json", "test_user_config.json", "user_config_backup.json"]
    
    for file in test_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"🧹 Cleaned up: {file}")
        except Exception as e:
            print(f"⚠️ Could not clean up {file}: {e}")

def main():
    """Run all tests."""
    print("🚀 USER CONFIGURATION SYSTEM TESTS")
    print("=" * 50)
    
    tests = [
        ("User Configuration Manager", test_user_config_manager),
        ("Order Execution Integration", test_order_execution_integration),
        ("Configuration File Handling", test_configuration_file_handling),
        ("Startup Integration", test_startup_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ User configuration system is ready for use")
    else:
        print("⚠️ Some tests failed - check the output above")
    
    # Cleanup
    print("\n🧹 CLEANUP")
    cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
