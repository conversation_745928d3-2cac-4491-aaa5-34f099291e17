{"timestamp": "2025-07-02T00:01:43.135338", "ensemble_signal": 0, "confidence": 0.8721901094918167, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6878013014793396, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6548080444335938, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8479817596147582, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999613761901855, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999921321868896, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.66, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:04:53.371248", "ensemble_signal": 0, "confidence": 0.9120531939505427, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8257724046707153, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7900236248970032, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9327060185251324, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999810457229614, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999959468841553, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.66, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:08:03.391601", "ensemble_signal": 0, "confidence": 0.9015024706027768, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9408345818519592, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.985889751552795, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8137751221656799, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8330392874630186, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999843835830688, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.54, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:11:13.335366", "ensemble_signal": 0, "confidence": 0.9046008658024834, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9975588321685791, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8178176879882812, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7860474220460837, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999847412109375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.54, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:14:23.174197", "ensemble_signal": 0, "confidence": 0.9129431304018968, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9975958466529846, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8472623229026794, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8316492534835287, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999817609786987, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.54, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:17:33.280185", "ensemble_signal": 0, "confidence": 0.8920708922937117, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8111658096313477, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.994563492063492, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7732580304145813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8596785310249525, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999748468399048, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999997615814209, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:20:43.138348", "ensemble_signal": 0, "confidence": 0.8771188603116318, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9845637083053589, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7283890031101532, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8273926973342896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7987490671043251, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999758005142212, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999997615814209, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:23:41.279861", "ensemble_signal": 0, "confidence": 0.8619946715109106, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.69158536195755, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9950714285714285, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7941731810569763, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7221344072955681, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999887943267822, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:26:41.164648", "ensemble_signal": 0, "confidence": 0.894722777431514, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.956572949886322, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9935885225885225, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8233054280281067, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7240596107792966, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999796152114868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:29:41.092091", "ensemble_signal": 0, "confidence": 0.9138373676475027, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.946774423122406, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9995, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8378610610961914, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8854198356124692, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999819993972778, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:32:41.170797", "ensemble_signal": 0, "confidence": 0.9259832015500012, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9927229285240173, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9974242424242425, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7593616843223572, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9194133291189711, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.99997878074646, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:35:41.412443", "ensemble_signal": 0, "confidence": 0.8687776404431734, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5139288306236267, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9992857142857143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8107797503471375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8300408842918996, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999659061431885, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999979734420776, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:38:41.674700", "ensemble_signal": 0, "confidence": 0.8553491137752602, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.45754149556159973, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.984342132505176, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8256486058235168, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7656394107173626, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999715089797974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:41:41.205665", "ensemble_signal": 0, "confidence": 0.9079272042628848, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8111700415611267, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666665, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8600295782089233, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8360026889322387, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999767541885376, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:44:41.289446", "ensemble_signal": 0, "confidence": 0.900845804257594, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7748923301696777, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666665, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8442730903625488, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8242976124022304, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999983549118042, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:47:41.325906", "ensemble_signal": 1, "confidence": 0.7656615192591708, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5635386109352112, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9717312409812408, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.252032995223999, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5487132293770245, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999573230743408, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999805688858032, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:50:41.477725", "ensemble_signal": 1, "confidence": 0.7778818496124155, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7422593832015991, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.540231144288567, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6616528630256653, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5018198963939818, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 1.0, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:53:41.281055", "ensemble_signal": 0, "confidence": 0.8338546000688739, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5090007781982422, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7354404926300049, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7052821346077224, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:56:41.153264", "ensemble_signal": 1, "confidence": 0.8356726990803511, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.48776960372924805, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7296754121780396, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7486766857909735, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999380111694336, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999948740005493, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T00:59:41.203064", "ensemble_signal": 1, "confidence": 0.8879722655911989, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9054352045059204, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6907033920288086, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8406274669294967, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.99998939037323, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:02:41.585124", "ensemble_signal": 1, "confidence": 0.7592689764005055, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6093754172325134, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9833531746031745, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.23618347942829132, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.44493208525426925, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999483823776245, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9996285438537598, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:05:41.093171", "ensemble_signal": 1, "confidence": 0.8526732919281031, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9383798241615295, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9589797008547007, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7493365406990051, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.47241714333520535, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999548196792603, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999918937683105, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:08:41.394070", "ensemble_signal": 0, "confidence": 0.8159039985866658, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4904479384422302, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9985714285714287, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6710602641105652, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6280938445991917, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999967098236084, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999957084655762, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:11:41.212857", "ensemble_signal": 1, "confidence": 0.8491452838744459, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5197006464004517, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9339615106582642, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6336765681623734, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999951124191284, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:14:41.434783", "ensemble_signal": 0, "confidence": 0.8339415071910211, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5020506381988525, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9994444444444445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8020979166030884, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6469088748009324, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999767541885376, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:17:41.291359", "ensemble_signal": 0, "confidence": 0.8221933317692972, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7188351154327393, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9645201465201464, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6946350932121277, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.49679510622448453, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999586343765259, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999961853027344, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:20:41.104477", "ensemble_signal": 1, "confidence": 0.845742510658884, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9190938472747803, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7299303660948998, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7282366752624512, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.604459434159827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999630451202393, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:23:41.167981", "ensemble_signal": 0, "confidence": 0.8531379873206537, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6837444305419922, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9886507936507937, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8506008982658386, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5252827750335244, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999638795852661, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:26:41.244540", "ensemble_signal": 0, "confidence": 0.8526406768321938, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6162585020065308, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8488339185714722, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5462217689819342, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999527931213379, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:29:41.166364", "ensemble_signal": 0, "confidence": 0.863608062351148, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5954412817955017, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8488814234733582, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6656850793583938, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999657869338989, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:32:41.483913", "ensemble_signal": 0, "confidence": 0.8604573575358354, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9420833587646484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9520873015873015, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8945809602737427, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.2904429744265654, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999600648880005, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999618530273438, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:35:41.175497", "ensemble_signal": 0, "confidence": 0.8805278349665339, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6533181667327881, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9762563381338133, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8916809558868408, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7385382644346845, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999589920043945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999980926513672, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:38:41.128387", "ensemble_signal": 0, "confidence": 0.9124751296071114, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8315151333808899, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.98875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8854712843894958, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8415656738359535, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999748468399048, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:41:41.138860", "ensemble_signal": 0, "confidence": 0.9035991990547182, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.740452766418457, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9984801587301588, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8891311287879944, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8393501327451861, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999794960021973, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:44:41.603720", "ensemble_signal": 0, "confidence": 0.9203593044294893, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9486148953437805, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9987301587301587, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8987923860549927, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7721051779504029, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999921321868896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:47:41.359054", "ensemble_signal": 0, "confidence": 0.86159800407136, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8779051303863525, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9943722099217767, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8562358617782593, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.395897859139716, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999735355377197, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999977350234985, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:50:41.242795", "ensemble_signal": 0, "confidence": 0.7895855531972278, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.9381350874900818, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -2, "confidence": 0.4190634840847654, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8529695868492126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.26613871274796613, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999963641166687, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999997615814209, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:53:41.050228", "ensemble_signal": 0, "confidence": 0.8648436997310529, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.8396468758583069, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8724333643913269, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4515867853972896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999268054962158, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999997615814209, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:56:41.109531", "ensemble_signal": 0, "confidence": 0.8845700878081473, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.9250546097755432, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9377627968788147, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.46843038465852216, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998842477798462, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T01:59:41.138273", "ensemble_signal": 0, "confidence": 0.885877303244643, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.9372502565383911, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9969397590361446, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9391594529151917, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.46958792148061673, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999593496322632, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:02:41.182938", "ensemble_signal": 0, "confidence": 0.924847480830262, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9939025640487671, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.808834433555603, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9610144834649144, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999558925628662, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999202489852905, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:05:41.304730", "ensemble_signal": 0, "confidence": 0.9078356220040086, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8231083750724792, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9992857142857143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8518129587173462, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9413833441214274, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999933123588562, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:08:41.105997", "ensemble_signal": 0, "confidence": 0.87254657861163, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.59644615650177, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9884095928226363, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8564831018447876, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8566003210133346, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999810457229614, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:11:41.344819", "ensemble_signal": 0, "confidence": 0.9281198463414465, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9907649755477905, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9926875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.876476526260376, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.938168745477683, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999817609786987, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:14:41.083655", "ensemble_signal": 0, "confidence": 0.9257300194521684, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9793302416801453, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9945, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8820686936378479, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.920681548476927, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999990701675415, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:17:41.231258", "ensemble_signal": 0, "confidence": 0.8909058377048298, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8850969076156616, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9536642262965793, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.940790593624115, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.7136309092745827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999737739562988, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999964237213135, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:20:41.344088", "ensemble_signal": 0, "confidence": 0.8823663156100562, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.801740288734436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.954657599285669, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9427146911621094, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.7172236571003476, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999963641166687, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999972581863403, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:23:41.263429", "ensemble_signal": 1, "confidence": 0.8720559710972653, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9314653873443604, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9379229239075787, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.90544193983078, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5487524620688545, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999221563339233, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:26:41.365833", "ensemble_signal": 0, "confidence": 0.8844431770318533, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9675922989845276, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9776666666666667, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9021093249320984, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5877132234664508, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999082088470459, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:29:41.394113", "ensemble_signal": 0, "confidence": 0.8826392437066928, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.969884991645813, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9819848484848486, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9419134855270386, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5250164160519639, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999544620513916, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:32:41.431865", "ensemble_signal": 1, "confidence": 0.8323943197887766, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7195507287979126, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9830411255411255, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6738836169242859, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5601190015107775, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999593496322632, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999953508377075, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:35:41.293737", "ensemble_signal": 1, "confidence": 0.8170932268712803, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9274957180023193, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.6822273049422563, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6857020258903503, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5034403949860918, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 1.0, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:38:41.446460", "ensemble_signal": 1, "confidence": 0.841082313518482, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8319789171218872, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9963888888888889, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7122774720191956, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.474126475568863, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999699592590332, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:41:41.423086", "ensemble_signal": 1, "confidence": 0.8076228748007039, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.5401363372802734, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7008985877037048, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.47510986717725456, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999619722366333, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:44:41.256268", "ensemble_signal": 1, "confidence": 0.8528437270964939, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.853731095790863, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7699053287506104, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.4994947269796841, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:47:41.099491", "ensemble_signal": 0, "confidence": 0.8320102841727273, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9807369709014893, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9955555555555555, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.2968279719352722, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6551441348935542, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999438524246216, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9998843669891357, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:50:41.218164", "ensemble_signal": 1, "confidence": 0.8727685863544593, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9137129783630371, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9857386363636362, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7636131644248962, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6369146628049234, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999505281448364, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:53:41.501367", "ensemble_signal": 1, "confidence": 0.8464141723190328, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6027172207832336, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.741523802280426, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7485266388554292, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999649524688721, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:56:41.263489", "ensemble_signal": 1, "confidence": 0.8600547109294362, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5529277920722961, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7294487953186035, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.828137802209808, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999831914901733, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999951124191284, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T02:59:41.389365", "ensemble_signal": 1, "confidence": 0.8623386591802713, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.48698699474334717, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.773909866809845, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8351713933749781, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999847412109375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:02:41.176298", "ensemble_signal": 0, "confidence": 0.8587306169711916, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5628541111946106, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9839583333333334, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9225171208381653, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6292983769792322, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999569654464722, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999909400939941, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:05:41.121892", "ensemble_signal": 0, "confidence": 0.8076449298015678, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5579231381416321, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.608012394894721, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8575040698051453, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.615396293351558, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999691247940063, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999996423721313, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:08:41.121679", "ensemble_signal": 0, "confidence": 0.8613411298853968, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8129697442054749, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.955625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8871817588806152, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4313235249313728, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999709129333496, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:11:41.175057", "ensemble_signal": 0, "confidence": 0.8622961837942318, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8274392485618591, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.988989898989899, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8853797316551208, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.3938786469676988, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999982476234436, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999959468841553, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:14:41.228441", "ensemble_signal": 0, "confidence": 0.8798453570447989, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9241583943367004, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9837301587301588, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8991983532905579, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.44654270223510617, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999796152114868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:17:41.292355", "ensemble_signal": 0, "confidence": 0.8561458391728803, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9026901125907898, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.965630305152364, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7822496891021729, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.4948861496310089, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999386072158813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999179840087891, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:20:41.541360", "ensemble_signal": 0, "confidence": 0.8632580330810332, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9251667261123657, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9479442640692644, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8424338698387146, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.4988623714496181, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999173879623413, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999979734420776, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:23:41.202914", "ensemble_signal": 0, "confidence": 0.8989022543112386, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9206944108009338, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9849572118702554, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8521033525466919, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7774059014682166, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999605417251587, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:26:41.219277", "ensemble_signal": 0, "confidence": 0.9271430124055468, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.997222900390625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.869899570941925, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9221847242045186, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999808073043823, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:29:41.075056", "ensemble_signal": 0, "confidence": 0.9221365296856452, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9650896787643433, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8748295307159424, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9043310720891431, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999794960021973, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:32:41.171162", "ensemble_signal": 0, "confidence": 0.8353346659802189, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.49158725142478943, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9850886384856973, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.611935019493103, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.8697640142222772, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999620914459229, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9996752738952637, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:35:41.340276", "ensemble_signal": 0, "confidence": 0.8785075429478902, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8304519653320312, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.951010461760462, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8069489002227783, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7632197968657068, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999462366104126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999908208847046, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:38:41.210240", "ensemble_signal": 0, "confidence": 0.8741172896151084, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8820690512657166, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9896349206349205, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8245195746421814, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6158566738333079, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999815225601196, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999994158744812, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:41:41.215219", "ensemble_signal": 0, "confidence": 0.8533316732151239, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6512216925621033, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9942777777777778, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.831409215927124, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6481009865092624, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999810457229614, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:44:41.487700", "ensemble_signal": 0, "confidence": 0.8559176467766423, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.610261857509613, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8369251489639282, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.701091779194098, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999850988388062, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:47:41.607263", "ensemble_signal": 0, "confidence": 0.8267625610726941, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5976161360740662, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9411219336219335, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7309306263923645, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5412311267535688, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999685287475586, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999949932098389, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:50:41.170193", "ensemble_signal": 0, "confidence": 0.8315867223591754, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5819122791290283, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.990125, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7200390696525574, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5272697742867508, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999411106109619, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999935626983643, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:53:41.302735", "ensemble_signal": 0, "confidence": 0.8222944078998318, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7335048913955688, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9870556526806528, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8857777714729309, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.6003696021447638, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.5289437174797058, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999983310699463, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:56:41.126394", "ensemble_signal": 0, "confidence": 0.8749651502075012, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9302148818969727, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9939685314685315, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8814369440078735, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5643751275298405, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8396928310394287, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999983310699463, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T03:59:41.286890", "ensemble_signal": 0, "confidence": 0.877621638577707, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9599571824073792, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.995, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8835513591766357, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4935079408849431, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8865801095962524, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999984502792358, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.68, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:02:41.285102", "ensemble_signal": 0, "confidence": 0.9310676019181923, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9969794750213623, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9934608303343243, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.765801191329956, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9534343305531866, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999976396560669, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999956488609314, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.67, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:05:41.830654", "ensemble_signal": 0, "confidence": 0.8967996924558341, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6654579639434814, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9907242063492063, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8118848204612732, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9331524710029073, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999815225601196, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999996542930603, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.67, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:08:41.554367", "ensemble_signal": 0, "confidence": 0.9020933083539213, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7194266319274902, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9916635610766045, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8218084573745728, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9159533408806614, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999886751174927, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.67, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:11:41.580089", "ensemble_signal": 0, "confidence": 0.9357059769960006, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9960801005363464, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8476778268814087, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9038553398066281, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999914169311523, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.675, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:14:41.354998", "ensemble_signal": 0, "confidence": 0.9315211064556781, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9922534823417664, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8328679800033569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8798292813185423, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999902248382568, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.68, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:17:41.113237", "ensemble_signal": 0, "confidence": 0.8563163547694551, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7618343234062195, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9881440781440783, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7234479784965515, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.5234513871828755, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999806880950928, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999890327453613, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.71, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:20:41.247602", "ensemble_signal": 0, "confidence": 0.8740058647362665, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7501606941223145, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9854354395604398, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7546197175979614, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6658686977432078, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999775886535645, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999909400939941, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.71, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:23:41.311622", "ensemble_signal": 0, "confidence": 0.8725230685940515, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5496746301651001, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.761424720287323, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.831631211804138, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999831914901733, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999994158744812, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.71, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:26:41.200043", "ensemble_signal": 0, "confidence": 0.9128875731053387, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9732528924942017, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.734168529510498, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7985976678758465, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999747276306152, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.71, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:29:41.304297", "ensemble_signal": 0, "confidence": 0.9200699263395293, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.942425549030304, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.731012225151062, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8972195145746571, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999771118164062, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.71, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:32:41.926413", "ensemble_signal": 0, "confidence": 0.8483971267805273, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4899705946445465, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9684322081427346, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7910096049308777, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6861982680756933, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999649524688721, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999988079071045, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.7, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:35:41.177863", "ensemble_signal": 0, "confidence": 0.902818303836855, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9945118427276611, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8433961003185917, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.826761782169342, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7607187886912254, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999768733978271, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999996423721313, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.7, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:38:41.289013", "ensemble_signal": 0, "confidence": 0.8818121949025213, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9961121678352356, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9994444444444445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8548988699913025, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.44150766224215365, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9443556070327759, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999912977218628, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.7, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:41:41.749744", "ensemble_signal": 0, "confidence": 0.8956749789498206, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9963394403457642, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9969444444444444, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8701438903808594, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4977456781862791, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999094009399414, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999922513961792, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.7, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:44:41.898976", "ensemble_signal": 0, "confidence": 0.8604919248840713, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5013319849967957, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8965732455253601, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.6591443396827801, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998780488967896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 1.0, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:47:41.217995", "ensemble_signal": 1, "confidence": 0.8701540185130551, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7076473832130432, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9820748556998558, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8413717746734619, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6103646890056862, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999972939491272, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999548196792603, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:50:41.240847", "ensemble_signal": 1, "confidence": 0.8887313046941521, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8185214996337891, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.966015873015873, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9168761372566223, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.607193442227684, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999796152114868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999954700469971, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:53:41.408419", "ensemble_signal": 1, "confidence": 0.8701764844794511, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7206875681877136, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9391332695082696, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9431649446487427, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5386214697645868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999821186065674, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:56:41.181100", "ensemble_signal": 1, "confidence": 0.8433894333794233, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.528225302696228, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9947321428571427, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9428672790527344, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.43470776988109505, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T04:59:41.129981", "ensemble_signal": 0, "confidence": 0.8812201707474842, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8761093616485596, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9947321428571427, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9421944618225098, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4329654158677154, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999981164932251, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.685, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:02:41.360740", "ensemble_signal": 0, "confidence": 0.8401954507907248, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5885360836982727, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9945194805194805, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6703707575798035, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7833739192503661, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999637603759766, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999953508377075, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:05:41.827590", "ensemble_signal": 1, "confidence": 0.8491139654446891, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9235237240791321, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7706452515401488, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6934243440628052, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7294611554854029, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999715089797974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 1.0, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:08:41.213322", "ensemble_signal": 1, "confidence": 0.9011317692132693, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9453774094581604, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9983333333333333, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7343534827232361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9071434502218965, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999833106994629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:11:41.434164", "ensemble_signal": 1, "confidence": 0.9003217074223342, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9739900231361389, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6998767256736755, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9040513244827121, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999821186065674, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999954700469971, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:14:41.198585", "ensemble_signal": 1, "confidence": 0.879181900216705, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7630650401115417, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6869157552719116, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.907674483105407, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999868869781494, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:17:41.252446", "ensemble_signal": 0, "confidence": 0.8525035828077728, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7736155390739441, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9377380952380951, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8744746446609497, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4567562366922948, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999967098236084, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999809265136719, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:20:41.685626", "ensemble_signal": 0, "confidence": 0.8356720512751042, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6116130948066711, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9599127685180318, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8383944630622864, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5862026976214225, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999931812286377, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999939203262329, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:23:41.198243", "ensemble_signal": 0, "confidence": 0.8506724994176063, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8565298318862915, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9834253246753248, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8587790727615356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.43233882615961056, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999802112579346, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:26:41.187891", "ensemble_signal": 0, "confidence": 0.8462006675887658, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7031400799751282, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9956230158730157, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8791065812110901, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.512946878383643, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999903440475464, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:29:41.434169", "ensemble_signal": 0, "confidence": 0.8542415545580895, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8039963841438293, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9987301587301587, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8987843990325928, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.46168003356184445, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999840259552002, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:32:41.240334", "ensemble_signal": 0, "confidence": 0.843837616484466, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8213346600532532, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9946713519354708, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.89579176902771, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.3577824281123172, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999600648880005, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999985694885254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:35:41.236691", "ensemble_signal": 0, "confidence": 0.8524745549154767, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9361160397529602, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9909156549971767, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8872877955436707, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.3329943568069367, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999587535858154, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999986886978149, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:38:41.304294", "ensemble_signal": 0, "confidence": 0.8583660471022645, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9984357953071594, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9950662251655629, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8943871259689331, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.3958048684306933, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.916606068611145, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:41:41.257028", "ensemble_signal": 0, "confidence": 0.8555377330945508, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9984682202339172, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9036869406700134, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.38681248725721135, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8858779668807983, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:44:41.281414", "ensemble_signal": 0, "confidence": 0.8612494662766054, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9985301494598389, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.996, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9016900062561035, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.41444240010850064, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8105871081352234, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999958276748657, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:47:41.136931", "ensemble_signal": 1, "confidence": 0.886527357277123, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8962711691856384, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9946428571428573, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.670691192150116, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.7522106719670965, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999357461929321, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999948740005493, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:50:41.880709", "ensemble_signal": 1, "confidence": 0.8218495246964285, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8155419826507568, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.502630097260685, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7258827686309814, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6876165604491905, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999746084213257, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 1.0, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:53:41.139358", "ensemble_signal": 1, "confidence": 0.8813017488444733, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7733500599861145, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9913888888888889, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8475561141967773, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6544427869735493, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999788999557495, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:56:41.213569", "ensemble_signal": 1, "confidence": 0.8998577932978071, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9428900480270386, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9925, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8474829196929932, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6508754812883594, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T05:59:41.266245", "ensemble_signal": 1, "confidence": 0.8934634037151822, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9624080061912537, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9925, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8486326932907104, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5726529980740621, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999779462814331, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:02:41.279567", "ensemble_signal": 0, "confidence": 0.849531563770627, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8642404079437256, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9468730158730158, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8665705323219299, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.44329818115341996, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999617338180542, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999840497970581, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:05:41.209491", "ensemble_signal": 0, "confidence": 0.8225082957990454, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5613272190093994, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9501062783002439, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.88507080078125, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.48110689886962255, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999971866607666, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999918937683105, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:08:41.292747", "ensemble_signal": 0, "confidence": 0.8381466077353867, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6189046502113342, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8898214285714288, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8466724753379822, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6629529203138388, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999715089797974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999967813491821, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:11:41.699388", "ensemble_signal": 0, "confidence": 0.8587338230150166, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8352794647216797, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9840555555555557, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8722372055053711, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5120533381232969, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999797344207764, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:14:41.408188", "ensemble_signal": 0, "confidence": 0.8943774618104702, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9847103953361511, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.986984126984127, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8775704503059387, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6751489296950562, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999842643737793, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:17:41.565265", "ensemble_signal": 0, "confidence": 0.8460149760062151, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8038139343261719, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9736107053872941, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8623122572898865, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4494302494965555, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999688863754272, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:20:41.182005", "ensemble_signal": 0, "confidence": 0.8507551888165336, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8001709580421448, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9845165945165946, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8629578351974487, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.484168176828945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999839067459106, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:23:41.591297", "ensemble_signal": 0, "confidence": 0.8645138288697921, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9889477491378784, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8937897086143494, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5233644085605537, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8495257496833801, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999971389770508, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:26:41.268804", "ensemble_signal": 0, "confidence": 0.8774659507459655, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9903704524040222, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9012656807899475, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5225778011385578, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9579827785491943, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999971389770508, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:29:53.606993", "ensemble_signal": 0, "confidence": 0.8772139655316707, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9908087253570557, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8988199234008789, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5221000405064449, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9581995606422424, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999977350234985, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:33:03.327945", "ensemble_signal": 1, "confidence": 0.854537539770429, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.4855785369873047, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9803333333333333, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7538034319877625, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8411732763000244, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999791383743286, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999704360961914, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:36:13.609168", "ensemble_signal": 0, "confidence": 0.837147110832343, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4736119508743286, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.85205697548066, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7087664604187012, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8699317019974301, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999573230743408, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999998807907104, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:39:23.467932", "ensemble_signal": 1, "confidence": 0.8751568722217727, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5872767567634583, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9988888888888888, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7392033934593201, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9210638484457527, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999843835830688, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999948740005493, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:42:33.451372", "ensemble_signal": 1, "confidence": 0.915921856729205, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9196954965591431, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7718700170516968, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.921753903443523, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999823570251465, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:45:43.420850", "ensemble_signal": 1, "confidence": 0.9174932281025308, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9166532158851624, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7929220795631409, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9178931396862074, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999756813049316, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:48:53.460275", "ensemble_signal": 1, "confidence": 0.8944900775753984, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9245588183403015, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9766884920634921, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7229804396629333, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7962260393918903, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999669790267944, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999902248382568, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:52:03.291992", "ensemble_signal": 0, "confidence": 0.8842160699421564, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8489707112312317, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9822222222222223, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8354758024215698, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6613057526532757, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999709129333496, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.63, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:55:13.418780", "ensemble_signal": 1, "confidence": 0.8986972768191761, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9199305772781372, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8445324301719666, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6613396011577126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T06:58:23.331501", "ensemble_signal": 1, "confidence": 0.9057329570658533, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8941608667373657, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8448072075843811, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.750147073437317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999982476234436, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:01:33.412250", "ensemble_signal": 0, "confidence": 0.8868815191252402, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8973397016525269, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9793313492063492, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6639272570610046, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7764690545473715, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999582767486572, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999083280563354, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.665, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:04:43.348699", "ensemble_signal": 0, "confidence": 0.8514136876994047, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4948643445968628, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9737217643467644, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7399833798408508, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7741970302087761, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999604225158691, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999996542930603, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.68, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:07:53.372802", "ensemble_signal": 1, "confidence": 0.8853587596049473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8323216438293457, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9886507936507937, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8355051875114441, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6267799976182278, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999721050262451, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.685, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:11:03.354673", "ensemble_signal": 1, "confidence": 0.8988202128812603, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9252151250839233, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8487316966056824, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6279667414299717, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999692440032959, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999994039535522, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:14:13.486215", "ensemble_signal": 1, "confidence": 0.867743210244743, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5113357901573181, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9994444444444445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8087744116783142, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8001563563676816, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999829530715942, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:17:23.336786", "ensemble_signal": 0, "confidence": 0.869688222586078, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6229865550994873, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8526739478111267, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6628209888075116, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999637603759766, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:20:33.454371", "ensemble_signal": 1, "confidence": 0.8530622399534399, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.47210338711738586, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9913198051948052, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8647918701171875, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6594563761452348, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998899698257446, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:23:43.512987", "ensemble_signal": 1, "confidence": 0.7934119551985312, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5852704048156738, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9989204545454545, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8704224228858948, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.45926191944588834, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.5368335247039795, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:26:53.476351", "ensemble_signal": 1, "confidence": 0.8531807790279473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6037423014640808, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8680274486541748, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.6320725597688012, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8847859501838684, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:30:03.452140", "ensemble_signal": 0, "confidence": 0.8640249976369643, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5766341090202332, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.996, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8657015562057495, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.7212974374596091, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9265931248664856, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999990463256836, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.69, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:33:13.621240", "ensemble_signal": 0, "confidence": 0.8660411920048131, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.735040545463562, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.998901098901099, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.722030758857727, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6234913647932839, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999414682388306, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999657869338989, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:36:24.110988", "ensemble_signal": 1, "confidence": 0.8577585566873349, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8204941153526306, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7401327875555813, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.766791045665741, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.67743713252161, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999723434448242, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999998807907104, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:39:47.905755", "ensemble_signal": 0, "confidence": 0.8932022873831834, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8315718770027161, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9616031746031746, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7685734033584595, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7621048515561415, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999948740005493, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:43:43.851571", "ensemble_signal": 0, "confidence": 0.9021613358300171, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8624343872070312, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9889285714285714, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.763391375541687, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7897151495756434, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:46:53.866181", "ensemble_signal": 0, "confidence": 0.820062452425554, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5287075042724609, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9768472222222222, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.43763577938079834, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7333098593944183, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999579191207886, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9991040825843811, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:50:03.670706", "ensemble_signal": 1, "confidence": 0.9008734840035186, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8884785771369934, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.94594681013431, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.776746392250061, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7917472113236598, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999505281448364, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999921321868896, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:53:13.767215", "ensemble_signal": 0, "confidence": 0.9081550969913044, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8302569389343262, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9735045093795094, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7656097412109375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8990658673283644, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999957084655762, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:56:23.961250", "ensemble_signal": 0, "confidence": 0.8904051576807477, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6859614253044128, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.987, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7379302382469177, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8977823496477878, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999976634979248, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999960660934448, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T07:59:33.670472", "ensemble_signal": 0, "confidence": 0.8969130757003829, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.692859411239624, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9936666666666667, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7448737025260925, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9358335740144978, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.99998939037323, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:02:43.561164", "ensemble_signal": 0, "confidence": 0.8803953883136437, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7794545292854309, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9581498015873016, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9091922640800476, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5619311145783893, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999574422836304, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9998736381530762, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:05:53.572384", "ensemble_signal": 1, "confidence": 0.8502636827024805, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.48208075761795044, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9747704240204241, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9046722650527954, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5758944578412402, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999614953994751, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999940395355225, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:09:03.908248", "ensemble_signal": 1, "confidence": 0.8752148595791813, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5581318140029907, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9809033613445379, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8766710162162781, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.7462453635594738, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999833106994629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:12:13.749928", "ensemble_signal": 0, "confidence": 0.8560784158069132, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.557773768901825, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9943028083028084, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8897234201431274, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5479325045218222, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999998807907104, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:15:23.921978", "ensemble_signal": 0, "confidence": 0.8922219252703631, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9751792550086975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.995017094017094, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8990710377693176, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.44574060699143303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999897480010986, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999998807907104, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:18:33.731002", "ensemble_signal": 1, "confidence": 0.9196384444446034, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9007978439331055, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.971839826839827, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8580565452575684, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8311382674323592, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999184608459473, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999953508377075, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:21:44.579812", "ensemble_signal": 1, "confidence": 0.8286661783345983, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5352160930633545, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.5943004784182326, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7383570671081543, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8751457457967678, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999768733978271, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999996423721313, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T08:24:54.416208", "ensemble_signal": 1, "confidence": 0.9099081572511181, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8085024356842041, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9822321428571428, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8408039212226868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8426629864055777, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999972939491272, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-02T11:43:08.733304", "ensemble_signal": 0, "confidence": 0.7583815068987295, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 0, "confidence": 0.2901269495487213, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.4249627147166891, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.793601930141449, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6018334579332961, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999186992645264, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999901056289673, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.715, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
