import pandas as pd
import numpy as np
from datetime import time, datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('DynamicConvergenceFilter')

class DynamicConvergenceFilter:
    def __init__(self, 
                 base_gap_threshold=0.0035,  # 0.35% default
                 min_gap_threshold=0.0010,   # 0.10% minimum
                 max_gap_threshold=0.0060,   # 0.60% maximum
                 adx_override_threshold=25,
                 confidence_override=0.75,
                 session_adjustments=None):
        """
        Dynamic EMA20/SMA50 convergence filter with volatility-based thresholds
        
        Args:
            base_gap_threshold: Base gap percentage (0.35%)
            min_gap_threshold: Minimum allowed gap threshold (0.10%)
            max_gap_threshold: Maximum allowed gap threshold (0.60%)
            adx_override_threshold: ADX value for trend strength override
            confidence_override: Minimum confidence for override
            session_adjustments: Dictionary of session adjustments
        """
        self.base_gap_threshold = base_gap_threshold
        self.min_gap_threshold = min_gap_threshold
        self.max_gap_threshold = max_gap_threshold
        self.adx_override_threshold = adx_override_threshold
        self.confidence_override = confidence_override
        
        # Session adjustments (GMT times)
        self.session_adjustments = session_adjustments or {
            'asian': {'time_range': (time(0,0), time(5,0)), 'threshold_multiplier': 0.7},
            'london': {'time_range': (time(7,0), time(10,0)), 'threshold_multiplier': 0.9},
            'new_york': {'time_range': (time(13,0), time(16,0)), 'threshold_multiplier': 1.1},
            'volatile': {'time_range': (time(18,0), time(21,0)), 'threshold_multiplier': 1.3}
        }
        
        # Track filtering statistics
        self.blocked_signals = 0
        self.total_signals = 0
        
    def calculate_atr(self, high, low, close, window=14):
        """Calculate Average True Range (ATR)"""
        tr = np.zeros(len(high))
        for i in range(1, len(high)):
            tr[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
        return pd.Series(tr).rolling(window).mean().iloc[-1]
    
    def calculate_adx(self, high, low, close, window=14):
        """Simplified ADX calculation"""
        # In production, replace with proper ADX calculation
        plus_dm = high.diff()
        minus_dm = -low.diff()
        dx = 100 * abs(plus_dm - minus_dm) / (plus_dm + minus_dm).replace(0, 0.001)
        return dx.rolling(window).mean().iloc[-1]
    
    def get_current_session(self):
        """Determine current trading session"""
        current_time = datetime.utcnow().time()
        for session, params in self.session_adjustments.items():
            start, end = params['time_range']
            if start <= current_time <= end:
                return session
        return 'default'
    
    def dynamic_gap_threshold(self, atr, atr_30d_avg):
        """
        Calculate dynamic gap threshold based on volatility
        Higher volatility → higher gap threshold
        """
        if atr_30d_avg == 0:  # Prevent division by zero
            return self.base_gap_threshold
            
        volatility_ratio = atr / atr_30d_avg
        dynamic_threshold = self.base_gap_threshold * volatility_ratio
        
        # Apply session adjustments
        session = self.get_current_session()
        if session in self.session_adjustments:
            dynamic_threshold *= self.session_adjustments[session]['threshold_multiplier']
        
        # Constrain within min/max bounds
        return max(self.min_gap_threshold, min(self.max_gap_threshold, dynamic_threshold))
    
    def should_allow_trade(self, ema20, sma50, adx, atr, atr_30d_avg, 
                           model_confidence=None, timeframe='medium'):
        """
        Determine if trade should be allowed based on convergence conditions
        
        Args:
            ema20: Current EMA20 value
            sma50: Current SMA50 value
            adx: Current ADX value
            atr: Current ATR value
            atr_30d_avg: 30-day average ATR
            model_confidence: Confidence score of the trading model (0-1)
            timeframe: Timeframe of the trading model (short/medium/long)
        """
        self.total_signals += 1
        
        # Calculate gap percentage
        price_reference = (ema20 + sma50) / 2
        gap_pct = abs(ema20 - sma50) / price_reference
        
        # Calculate dynamic threshold
        threshold = self.dynamic_gap_threshold(atr, atr_30d_avg)
        
        # Base convergence check
        gap_sufficient = gap_pct >= threshold
        
        # Override conditions
        strong_trend_override = adx >= self.adx_override_threshold
        confidence_override = (model_confidence is not None and 
                              model_confidence >= self.confidence_override)
        
        # Timeframe-specific rules
        if timeframe == 'long' and strong_trend_override:
            # Allow long-term models in strong trends even with smaller gaps
            gap_sufficient = gap_pct >= (threshold * 0.7)
        
        # Decision logic
        if gap_sufficient:
            logger.info(f"✅ Gap sufficient: {gap_pct:.3f}% >= {threshold:.3f}%")
            return True
        elif strong_trend_override and confidence_override:
            logger.info(f"⚠️ Override applied: ADX={adx}, Confidence={model_confidence}")
            return True
        else:
            self.blocked_signals += 1
            logger.info(f"🚫 Gap insufficient: {gap_pct:.3f}% < {threshold:.3f}%")
            logger.info(f"    Blocked signals: {self.blocked_signals}/{self.total_signals} "
                       f"({self.blocked_signals/self.total_signals:.1%})")
            return False

    def get_filter_status(self):
        """Return current filter statistics"""
        return {
            'blocked_signals': self.blocked_signals,
            'total_signals': self.total_signals,
            'block_rate': self.blocked_signals / self.total_signals if self.total_signals else 0
        }

# Integration with your trading system
class TradingSignalGenerator:
    def __init__(self):
        self.convergence_filter = DynamicConvergenceFilter(
            base_gap_threshold=0.0015,  # Start with lower threshold
            adx_override_threshold=22,
            confidence_override=0.70
        )
        # Initialize with historical ATR average
        self.atr_30d_avg = 150.0  # Example value, replace with actual calculation
    
    def generate_signal(self, ohlc_data, model_type, model_confidence):
        """
        Generate trading signal with convergence filtering
        
        Args:
            ohlc_data: DataFrame with OHLC data
            model_type: 'short', 'medium', or 'long'
            model_confidence: Model's confidence score (0-1)
        """
        # Calculate indicators
        ema20 = ohlc_data['close'].ewm(span=20).mean().iloc[-1]
        sma50 = ohlc_data['close'].rolling(50).mean().iloc[-1]
        adx = self.convergence_filter.calculate_adx(
            ohlc_data['high'], ohlc_data['low'], ohlc_data['close'])
        atr = self.convergence_filter.calculate_atr(
            ohlc_data['high'], ohlc_data['low'], ohlc_data['close'])
        
        # Check convergence filter
        allow_trade = self.convergence_filter.should_allow_trade(
            ema20, sma50, adx, atr, self.atr_30d_avg, 
            model_confidence, timeframe=model_type
        )
        
        # Log decision details
        logger.info(f"EMA20: {ema20:.2f}, SMA50: {sma50:.2f}, Gap: {abs(ema20-sma50):.2f} ({abs(ema20-sma50)/sma50:.3%})")
        logger.info(f"ADX: {adx:.2f}, ATR: {atr:.2f}, 30d ATR Avg: {self.atr_30d_avg:.2f}")
        
        return allow_trade

# Example usage
if __name__ == "__main__":
    # Mock data (replace with real OHLC data)
    data = pd.DataFrame({
        'open': [54600, 54650, 54700, 54680],
        'high': [54720, 54780, 54800, 54750],
        'low': [54580, 54620, 54650, 54670],
        'close': [54650, 54720, 54750, 54730]
    })
    
    signal_generator = TradingSignalGenerator()
    
    # Simulate signals for different timeframes
    timeframes = ['short', 'medium', 'long']
    confidences = [0.82, 0.75, 0.68]
    
    for tf, conf in zip(timeframes, confidences):
        logger.info(f"\n=== Checking {tf.upper()} TERM model (Confidence: {conf}) ===")
        allowed = signal_generator.generate_signal(data, tf, conf)
        logger.info(f"Trade allowed: {'YES' if allowed else 'NO'}")