<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEX 900 DOWN Index Trend Analysis</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --text-color: #ecf0f1;
            --uptrend-color: #2ecc71;
            --downtrend-color: #e74c3c;
            --neutral-color: #f39c12;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        .dashboard {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }

        .header {
            background-color: var(--primary-color);
            color: var(--text-color);
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid var(--accent-color);
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: var(--secondary-color);
            color: var(--text-color);
        }

        .price {
            font-size: 22px;
            font-weight: bold;
        }

        .price-change {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .price-up {
            background-color: var(--uptrend-color);
        }

        .price-down {
            background-color: var(--downtrend-color);
        }

        .price-flat {
            background-color: var(--neutral-color);
        }

        .timestamp {
            font-size: 14px;
            opacity: 0.8;
        }

        .overall-trend {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .trend-strength {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
        }

        .strength-bar {
            width: 80%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .strength-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .strength-text {
            margin-left: 10px;
            font-weight: bold;
        }

        .trend-duration {
            font-weight: bold;
            color: #34495e;
            margin-top: 10px;
        }

        .trend-status {
            font-style: italic;
            color: #7f8c8d;
            margin-top: 10px;
        }

        .trend-change-alert {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            animation: blink 1.5s infinite;
        }

        .trend-change-alert.uptrend {
            background-color: rgba(46, 204, 113, 0.3);
            color: #27ae60;
            border: 2px solid #27ae60;
        }

        .trend-change-alert.downtrend {
            background-color: rgba(231, 76, 60, 0.3);
            color: #c0392b;
            border: 2px solid #c0392b;
        }

        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .timeframes {
            padding: 20px;
        }

        .timeframe {
            margin-bottom: 20px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .timeframe-header {
            padding: 10px 15px;
            color: white;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .timeframe-content {
            padding: 15px;
            background-color: white;
            border: 1px solid #eee;
            border-top: none;
        }

        .interval {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .interval:last-child {
            border-bottom: none;
        }

        .interval-name {
            font-weight: bold;
            width: 15%;
        }

        .interval-duration {
            width: 85%;
            text-align: right;
        }

        .uptrend {
            background-color: var(--uptrend-color);
        }

        .downtrend {
            background-color: var(--downtrend-color);
        }

        .sideways {
            background-color: var(--neutral-color);
        }

        .footer {
            padding: 15px;
            text-align: center;
            background-color: #f9f9f9;
            color: #7f8c8d;
            font-size: 14px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .dashboard {
                margin: 0;
            }

            .price-info {
                flex-direction: column;
                text-align: center;
            }

            .price, .price-change, .timestamp {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>DEX 900 DOWN INDEX TREND ANALYSIS</h1>
        </div>

        <div class="price-info">
            <div class="price">CURRENT PRICE: <span id="current-price">61305.54</span></div>
            <div class="price-change price-down">DOWN -11.42</div>
            <div class="timestamp">Last Updated: 2025-05-14 06:24:44</div>
        </div>

        <div class="overall-trend">
            <h2>CURRENT OVERALL TREND: WEAK UPTREND</h2>
            <div class="trend-strength">
                <div class="strength-bar">
                    <div class="strength-fill" style="width: 51.2%; background-color: #27ae60;"></div>
                </div>
                <div class="strength-text">51.2%</div>
            </div>
            <div class="trend-duration">DURATION: Approximately 9 hours 22 minutes</div>
            <div class="trend-status">STATUS: This trend is stable with no significant signs of change</div>
            
        </div>

        <div class="timeframes">
            <h2>Timeframe Breakdown</h2>

            <div class="timeframe">
                <div class="timeframe-header {{short_term_class}}">
                    <span>Short-Term</span>
                    <span>{{short_term_category}}</span>
                </div>
                <div class="timeframe-content">
                    {{short_term_intervals}}
                </div>
            </div>

            <div class="timeframe">
                <div class="timeframe-header {{medium_term_class}}">
                    <span>Medium-Term</span>
                    <span>{{medium_term_category}}</span>
                </div>
                <div class="timeframe-content">
                    {{medium_term_intervals}}
                </div>
            </div>

            <div class="timeframe">
                <div class="timeframe-header {{long_term_class}}">
                    <span>Long-Term</span>
                    <span>{{long_term_category}}</span>
                </div>
                <div class="timeframe-content">
                    {{long_term_intervals}}
                </div>
            </div>
        </div>

        <div class="footer">
            For detailed information, check the reports directory.
        </div>
    </div>
</body>
</html>
