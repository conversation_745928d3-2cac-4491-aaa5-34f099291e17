#!/usr/bin/env python3
"""
AI Trading System Startup with User Configuration
Prompts user for lot size configuration before starting the trading system.
"""

import os
import sys
import time
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system_startup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('TradingSystemStartup')

def main():
    """Main startup function with user configuration."""
    try:
        print("🚀 AI TRADING SYSTEM STARTUP")
        print("=" * 50)
        print("Starting AI Trading System with User Configuration")
        print("=" * 50)
        
        # Step 1: User Configuration
        print("\n📋 STEP 1: USER CONFIGURATION")
        print("-" * 30)
        
        if not configure_user_settings():
            print("❌ User configuration failed. Exiting.")
            return False
        
        # Step 2: Validate Configuration
        print("\n✅ STEP 2: CONFIGURATION VALIDATION")
        print("-" * 35)
        
        if not validate_configuration():
            print("❌ Configuration validation failed. Exiting.")
            return False
        
        # Step 3: Start Trading System
        print("\n🎯 STEP 3: STARTING TRADING SYSTEM")
        print("-" * 35)
        
        if not start_trading_system():
            print("❌ Trading system startup failed. Exiting.")
            return False
        
        print("\n🎉 SUCCESS: AI Trading System started successfully!")
        print("📊 Dashboard: http://localhost:5000")
        print("🔄 System is now running with your configuration")
        print("Press Ctrl+C to stop the system")
        
        # Keep running
        try:
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested by user")
            return True
            
    except Exception as e:
        logger.error(f"Startup error: {e}")
        print(f"\n❌ Startup error: {e}")
        return False

def configure_user_settings():
    """Configure user settings including manual lot size."""
    try:
        from user_config_manager import UserConfigManager
        
        print("Initializing user configuration manager...")
        config_manager = UserConfigManager()
        
        # Check if configuration already exists
        existing_config = config_manager.load_user_config()
        
        if existing_config:
            print("✅ Found existing configuration")
            
            # Display current settings
            lot_config = existing_config.get("lot_size", {})
            print(f"Current lot size mode: {lot_config.get('mode', 'auto')}")
            if lot_config.get('mode') == 'manual':
                print(f"Current manual lot size: {lot_config.get('manual_lot_size', 0.01)}")
            
            # Ask if user wants to modify
            modify = input("\nDo you want to modify your configuration? (y/N): ").strip().lower()
            if modify not in ['y', 'yes']:
                print("✅ Using existing configuration")
                return True
        
        # Prompt for new configuration
        success = config_manager.prompt_user_configuration()
        
        if success:
            print("✅ User configuration completed successfully")
            return True
        else:
            print("❌ User configuration failed or was cancelled")
            return False
            
    except ImportError:
        print("❌ User configuration manager not available")
        print("💡 Using default automatic lot size calculation")
        return True
    except Exception as e:
        logger.error(f"User configuration error: {e}")
        print(f"❌ Configuration error: {e}")
        return False

def validate_configuration():
    """Validate the configuration is properly set up."""
    try:
        import json
        import os
        
        print("Validating configuration files...")
        
        # Check if user config exists
        config_file = "user_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Validate structure
                if "lot_size" in config:
                    lot_config = config["lot_size"]
                    mode = lot_config.get("mode", "auto")
                    
                    print(f"✅ Lot size mode: {mode}")
                    
                    if mode == "manual":
                        manual_lot = lot_config.get("manual_lot_size")
                        if manual_lot and manual_lot > 0:
                            print(f"✅ Manual lot size: {manual_lot}")
                        else:
                            print("❌ Invalid manual lot size")
                            return False
                    
                    print("✅ Configuration validation passed")
                    return True
                else:
                    print("❌ Invalid configuration structure")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ Invalid JSON in configuration file")
                return False
        else:
            print("ℹ️ No user configuration file found - using defaults")
            return True
            
    except Exception as e:
        logger.error(f"Configuration validation error: {e}")
        print(f"❌ Validation error: {e}")
        return False

def start_trading_system():
    """Start the trading system components."""
    try:
        print("Starting trading system components...")
        
        # Option 1: Use system orchestrator (comprehensive)
        try:
            from system_orchestrator import AITradingSystemOrchestrator
            
            print("Using comprehensive system orchestrator...")
            orchestrator = AITradingSystemOrchestrator()
            
            # Skip user configuration phase since we already did it
            orchestrator.phases['user_configuration'] = True
            
            # Start remaining phases
            success = orchestrator.start_complete_system()
            
            if success:
                print("✅ Trading system started via orchestrator")
                return True
            else:
                print("❌ Orchestrator startup failed, trying standalone...")
                
        except Exception as e:
            logger.warning(f"Orchestrator startup failed: {e}")
            print("⚠️ Orchestrator failed, trying standalone startup...")
        
        # Option 2: Standalone trading engine (fallback)
        try:
            from trading_engine import TradingEngine
            
            print("Starting standalone trading engine...")
            engine = TradingEngine()
            
            if not engine.initialize_components():
                print("❌ Failed to initialize trading components")
                return False
            
            if not engine.start_trading():
                print("❌ Failed to start trading system")
                return False
            
            print("✅ Standalone trading engine started successfully")
            
            # Start dashboard if available
            try:
                import subprocess
                import sys
                
                print("Starting dashboard server...")
                dashboard_process = subprocess.Popen([
                    sys.executable, 'dashboard_server.py'
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                time.sleep(5)  # Give dashboard time to start
                
                if dashboard_process.poll() is None:
                    print("✅ Dashboard server started")
                else:
                    print("⚠️ Dashboard server failed to start")
                    
            except Exception as dashboard_e:
                logger.warning(f"Dashboard startup failed: {dashboard_e}")
                print("⚠️ Dashboard startup failed, continuing without dashboard")
            
            return True
            
        except Exception as e:
            logger.error(f"Standalone startup failed: {e}")
            print(f"❌ Standalone startup failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"Trading system startup error: {e}")
        print(f"❌ Trading system startup error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting tips:")
        print("   - Make sure MT5 is running and logged in")
        print("   - Check that all dependencies are installed")
        print("   - Verify AI models are trained (run train_all_models.py)")
        print("   - Check logs for detailed error information")
    
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
