{"timestamp": "2025-06-24T22:04:25.801908", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3821565806865692, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:04:25.866323", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.3575005116095694, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:04:25.871416", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:04:26.086098", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9617144465446472, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:04:26.149771", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4558606889664494, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:04:26.159818", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999922513961792, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:04:26.270446", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999996542930603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:04:26.332896", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:04:26.337221", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:07:24.009884", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4942712187767029, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:07:24.094209", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8818433091560205, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:07:24.099319", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:07:24.158474", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9990905523300171, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:07:24.219817", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5368953777646771, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:07:24.229871", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999635219573975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:07:24.302442", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:07:24.368407", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:07:24.373717", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:10:23.974763", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4942712187767029, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:10:24.040919", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8818433091560205, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:10:24.045924", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:10:24.102985", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9990905523300171, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:10:24.163012", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5368953777646771, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:10:24.172020", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999635219573975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:10:24.244592", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:10:24.308218", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:10:24.313349", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:13:24.171031", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4942712187767029, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:13:24.809464", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8818433091560203, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:13:24.814595", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:13:24.871249", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9990905523300171, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:13:24.930650", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5368953777646771, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:13:24.940248", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999635219573975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:13:25.012375", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:13:25.076861", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:13:25.081936", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:16:23.896717", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5998777747154236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:16:23.970389", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.6934210976196329, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:16:23.975424", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:16:24.032434", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9681374430656433, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:16:24.092399", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.477240795297103, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:16:24.102086", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999144077301025, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:16:24.173662", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999935626983643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:16:24.239363", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:16:24.244496", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:19:23.954897", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6970434188842773, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:19:24.053556", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7990917066899168, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:19:24.058574", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:19:24.116802", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9945476055145264, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:19:24.177220", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5543466598390426, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:19:24.186682", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99993896484375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:19:24.259253", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999798536300659, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:19:24.325518", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:19:24.330685", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:22:23.919527", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2976459860801697, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:22:24.937143", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.40035895938676286, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:22:24.942264", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:22:24.998039", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.8386719226837158, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:22:25.059101", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.47167361006829006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:22:25.068196", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999681711196899, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:22:25.142268", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999991774559021, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:22:25.216156", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.55, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:22:25.221211", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:25:23.920157", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.2697419822216034, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:25:23.998757", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5869031341011527, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:25:24.003900", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:25:24.060099", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.8390283584594727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:25:24.121132", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4964294750274889, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:25:24.130359", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999594688415527, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:25:24.203426", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999929666519165, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:25:24.266691", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:25:24.272003", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:28:23.990827", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.47700732946395874, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:28:24.052538", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.795813248113453, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:28:24.057626", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:28:24.114186", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.8381282091140747, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:28:24.174328", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.46183226499010055, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:28:24.183419", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999654293060303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:28:24.254543", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999955892562866, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:28:24.319896", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:28:24.324913", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:31:23.873213", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2896387279033661, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:31:23.942376", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.989384370096734, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:31:23.947454", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:31:24.004960", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.675517201423645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:31:24.065872", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9005724723251199, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:31:24.074931", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999780654907227, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:31:24.146819", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999916553497314, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:31:24.210292", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:31:24.215438", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:34:23.926120", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.32917067408561707, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:34:23.997274", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9854999999999999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:34:24.002317", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:34:24.059298", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998500347137451, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:34:24.119245", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8862683508545415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:34:24.128298", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999746084213257, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:34:24.199429", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999945163726807, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:34:24.263625", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:34:24.268681", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:37:23.876540", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4078032076358795, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:37:24.020824", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998125, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:37:24.025849", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:37:24.082994", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998993873596191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:37:24.143702", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7916127554087204, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:37:24.152744", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999775886535645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:37:24.225431", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:37:24.287814", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:37:24.292856", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:40:23.897630", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5512768626213074, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:40:24.088518", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9972222222222222, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:40:24.093523", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:40:24.151304", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.999948263168335, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:40:24.210809", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5995904895409759, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:40:24.219819", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9887639284133911, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:40:24.290994", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:40:24.356163", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:40:24.361237", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:43:23.854440", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3694806694984436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:43:23.929174", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:43:23.949514", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:43:24.018272", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999250173568726, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:43:24.076600", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8521757566943147, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:43:24.531264", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999750852584839, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:43:24.612619", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:43:24.676247", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:43:24.704603", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:46:23.884318", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.4941248297691345, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:46:23.946356", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:46:23.956484", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:46:24.012307", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.5330170392990112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:46:24.071592", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8849834378324549, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:46:24.109195", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999656677246094, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:46:24.190820", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999972581863403, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:46:24.254597", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5398809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:46:24.283694", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:49:23.872860", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4803406298160553, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:49:23.932557", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9867253579753579, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:49:23.937627", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:49:23.992078", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9987030029296875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:49:24.064100", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9477048361834264, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:49:24.072138", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999755620956421, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:49:24.143321", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999871253967285, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:49:24.243138", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.615, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:49:24.248728", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:52:23.941292", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.46969446539878845, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:52:24.313722", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:52:24.318808", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:52:24.375211", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997536540031433, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:52:24.435481", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9533631084666985, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:52:24.444567", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999675750732422, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:52:24.516342", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999955892562866, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:52:24.582489", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.615, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:52:24.588018", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:55:23.854427", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9541813731193542, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:55:24.847921", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9998780487804878, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:55:24.852941", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:55:24.910387", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9996078610420227, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:55:24.970359", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9441784010520731, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:55:24.979629", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999667406082153, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:55:25.052273", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:55:25.114593", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.615, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:55:25.119951", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T22:58:24.087546", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.898564875125885, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T22:58:24.258015", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9998780487804878, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T22:58:24.263249", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T22:58:24.319246", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9991294741630554, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T22:58:24.380285", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9441784010520731, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T22:58:24.389322", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999966025352478, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T22:58:24.462892", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999960660934448, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T22:58:24.526314", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T22:58:24.531618", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:01:23.982721", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5149202346801758, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:01:24.063308", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9903968253968255, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:01:24.068390", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:01:24.124362", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.639388918876648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:01:24.184067", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8787875989491155, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:01:24.193143", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999567270278931, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:01:24.265209", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999985694885254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:01:24.328693", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:01:24.333757", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:04:23.958766", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5368033647537231, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:04:24.034746", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9914761904761904, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:04:24.039785", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:04:24.095259", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865143895149231, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:04:24.155437", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9159449294235195, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:04:24.164515", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999533891677856, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:04:24.236139", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999926090240479, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:04:24.298938", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.515, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:04:24.304004", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:07:24.003965", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.619720995426178, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:07:24.066987", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9957142857142857, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:07:24.072057", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:07:24.130034", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9992794394493103, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:07:24.190339", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8937890362662787, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:07:24.217465", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999969482421875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:07:24.289082", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:07:24.355082", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:07:24.360109", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:10:23.968393", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7436364889144897, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:10:24.045366", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.990241144018583, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:10:24.055298", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:10:24.121908", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9995759129524231, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:10:24.181248", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9603508000416892, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:10:24.190568", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999653100967407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:10:24.262330", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:10:24.326846", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:10:24.331907", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:13:23.906882", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6936835050582886, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:13:23.972781", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.990241144018583, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:13:23.978329", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:13:24.033959", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9993283748626709, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:13:24.095495", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9697361443039124, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:13:24.104534", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999769926071167, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:13:24.176601", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999960660934448, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:13:24.241396", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:13:24.246424", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:16:24.028581", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.526127278804779, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:16:24.089683", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9780623543123542, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:16:24.094717", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:16:24.152726", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.6246556639671326, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:16:24.212366", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9551919029784197, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:16:24.221416", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:16:24.293985", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:16:24.357567", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.5701190476190476, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:16:24.362585", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:19:24.016918", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.4188432991504669, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:19:24.094599", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9806099733599735, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:19:24.100163", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:19:24.157175", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9247108697891235, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:19:24.216508", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.951992950846541, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:19:24.225604", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999653100967407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:19:24.297172", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999873638153076, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:19:24.360298", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:19:24.366698", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:22:23.942639", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.44371727108955383, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:22:24.019909", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9873333333333332, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:22:24.024926", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:22:24.080982", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998335838317871, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:22:24.142196", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8536391178266177, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:22:24.151362", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999967098236084, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:22:24.222426", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999830722808838, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:22:24.288814", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:22:24.293883", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:25:23.983581", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.777131199836731, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:25:24.075046", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9998780487804878, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:25:24.079637", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:25:24.135185", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997232556343079, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:25:24.195015", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8866646467668892, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:25:24.204076", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:25:24.276148", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:25:24.339242", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:25:24.344496", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:28:23.930080", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.908033013343811, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:28:24.136608", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9998780487804878, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:28:24.141646", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:28:24.195400", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9995803236961365, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:28:24.255339", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9524480871558323, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:28:24.283660", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999688863754272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:28:24.357471", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:28:24.418610", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:28:24.444878", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:31:23.870574", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6192478537559509, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:31:24.304549", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7664668535437427, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:31:24.309621", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:31:24.366623", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.674816370010376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:31:24.426447", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5457077502726468, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:31:24.435485", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999706745147705, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:31:24.508055", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999985694885254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:31:24.573322", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:31:24.577843", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:34:23.912454", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.34712111949920654, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:34:24.325889", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9627251221001221, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:34:24.330971", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:34:24.386786", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9822227954864502, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:34:24.445707", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.48904511608602136, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:34:24.454957", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999653100967407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:34:24.527028", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999938011169434, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:34:24.590833", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:34:24.596225", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:37:23.903198", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5170419812202454, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:37:24.091749", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9778037825059104, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:37:24.095754", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:37:24.149739", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.996032178401947, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:37:24.208792", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.7118994503077546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:37:24.231169", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999707937240601, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:37:24.302319", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999972581863403, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:37:24.367686", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:37:24.389087", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:40:23.900842", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5152997374534607, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:40:23.976067", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9941477272727272, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:40:23.980899", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:40:24.034613", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9969596862792969, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:40:24.093501", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6498068761931625, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:40:24.124009", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999731779098511, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:40:24.194903", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999972581863403, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:40:24.259691", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:40:24.285161", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:43:23.873977", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4480401277542114, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:43:23.952898", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:43:23.957964", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:43:24.015946", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999388456344604, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:43:24.074854", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.42237553671820266, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:43:24.083863", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999802112579346, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:43:24.155957", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:43:24.221919", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:43:24.226924", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:46:23.917714", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4687613546848297, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:46:23.986291", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6341919450108837, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:46:23.992052", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:46:24.047190", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.6754093170166016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:46:24.108189", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.4475210334487418, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:46:24.123745", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999779462814331, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:46:24.194003", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999949932098389, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:46:24.256376", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:46:24.280194", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:49:23.927021", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.45440810918807983, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:49:23.994627", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8874859063855657, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:49:23.999651", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:49:24.056200", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998788833618164, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:49:24.116107", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.3885578730256988, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:49:24.125455", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999579191207886, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:49:24.197534", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999886751174927, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:49:24.259948", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:49:24.265459", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:52:23.949036", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.48651519417762756, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:52:24.011049", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:52:24.015227", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:52:24.073110", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999052286148071, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:52:24.132687", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5663489804122793, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:52:24.142409", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999560117721558, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:52:24.213479", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999964237213135, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:52:24.277956", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:52:24.283575", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:55:23.965483", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4578334093093872, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:55:24.310873", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:55:24.314939", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:55:24.369466", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999179840087891, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:55:24.431447", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5666091659018143, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:55:24.458943", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:55:24.529257", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999963045120239, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:55:24.592362", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:55:24.619000", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-24T23:58:23.874313", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2551141083240509, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-24T23:58:23.938004", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-24T23:58:23.943029", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-24T23:58:23.998989", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999216794967651, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-24T23:58:24.059422", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5852623214923112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-24T23:58:24.068713", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999785423278809, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-24T23:58:24.139940", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999960660934448, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-24T23:58:24.204468", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-24T23:58:24.209822", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
